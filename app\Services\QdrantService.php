<?php

namespace App\Services;

use Qdrant\Client;
use Qdrant\Http\Builder;
use Qdrant\Models\Request\CreateCollection;
use Qdrant\Models\Request\VectorParams;
use Qdrant\Models\Request\SearchRequest;
use Qdrant\Models\Request\UpsertRequest;
use Qdrant\Models\Request\DeleteRequest;
use Qdrant\Models\PointStruct;
use Qdrant\Models\Filter\Filter;
use Qdrant\Models\Filter\Condition\MatchCondition;
use Qdrant\Models\Filter\Condition\FieldCondition;
use Illuminate\Support\Facades\Log;
use Exception;

class QdrantService
{
    private Client $client;
    private string $collectionName;
    private int $vectorSize;
    private string $distanceMetric;

    public function __construct()
    {
        $config = config('qdrant');
        
        $this->client = new Client(
            Builder::create(
                $config['host'],
                $config['port']
            )->withApiKey($config['api_key'])
             ->withTimeout($config['timeout'])
             ->build()
        );
        
        $this->collectionName = $config['collections']['vectors']['name'];
        $this->vectorSize = $config['collections']['vectors']['vector_size'];
        $this->distanceMetric = $config['collections']['vectors']['distance'];
        
        $this->ensureCollectionExists();
    }

    /**
     * Ensure the collection exists, create if it doesn't
     */
    private function ensureCollectionExists(): void
    {
        try {
            $this->client->collections($this->collectionName)->info();
        } catch (Exception $e) {
            // Collection doesn't exist, create it
            $this->createCollection();
        }
    }

    /**
     * Create the collection
     */
    private function createCollection(): void
    {
        $createCollection = new CreateCollection();
        $createCollection->addVector(
            new VectorParams($this->vectorSize, $this->distanceMetric),
            'default'
        );

        $this->client->collections($this->collectionName)->create($createCollection);
        Log::info("Qdrant collection '{$this->collectionName}' created successfully");
    }

    /**
     * Insert or update vectors in Qdrant
     *
     * @param array $vectors Array of vectors with metadata
     * @return bool
     */
    public function upsertVectors(array $vectors): bool
    {
        try {
            $points = [];
            
            foreach ($vectors as $vector) {
                $point = new PointStruct(
                    $vector['id'],
                    $vector['vector'],
                    $vector['payload'] ?? []
                );
                $points[] = $point;
            }

            $upsertRequest = new UpsertRequest();
            $upsertRequest->addPoints($points);

            $this->client->collections($this->collectionName)->points()->upsert($upsertRequest);
            
            return true;
        } catch (Exception $e) {
            Log::error('Failed to upsert vectors to Qdrant: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Search for similar vectors
     *
     * @param array $queryVector
     * @param int $limit
     * @param array $filter
     * @return array
     */
    public function searchSimilarVectors(array $queryVector, int $limit = 10, array $filter = []): array
    {
        try {
            $searchRequest = new SearchRequest($queryVector, $limit);
            
            if (!empty($filter)) {
                $searchRequest->setFilter($this->buildFilter($filter));
            }

            $response = $this->client->collections($this->collectionName)->points()->search($searchRequest);
            
            return $response->getResult();
        } catch (Exception $e) {
            Log::error('Failed to search vectors in Qdrant: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Delete vectors by IDs
     *
     * @param array $ids
     * @return bool
     */
    public function deleteVectors(array $ids): bool
    {
        try {
            $deleteRequest = new DeleteRequest();
            $deleteRequest->addPointId($ids);

            $this->client->collections($this->collectionName)->points()->delete($deleteRequest);
            
            return true;
        } catch (Exception $e) {
            Log::error('Failed to delete vectors from Qdrant: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Delete vectors by filter
     *
     * @param array $filter
     * @return bool
     */
    public function deleteVectorsByFilter(array $filter): bool
    {
        try {
            $deleteRequest = new DeleteRequest();
            $deleteRequest->setFilter($this->buildFilter($filter));

            $this->client->collections($this->collectionName)->points()->delete($deleteRequest);
            
            return true;
        } catch (Exception $e) {
            Log::error('Failed to delete vectors by filter from Qdrant: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Build filter for Qdrant queries
     *
     * @param array $conditions
     * @return Filter
     */
    private function buildFilter(array $conditions): Filter
    {
        $filter = new Filter();
        
        foreach ($conditions as $field => $value) {
            $condition = new FieldCondition($field, new MatchCondition($value));
            $filter->addMustCondition($condition);
        }
        
        return $filter;
    }

    /**
     * Get collection info
     *
     * @return array
     */
    public function getCollectionInfo(): array
    {
        try {
            return $this->client->collections($this->collectionName)->info();
        } catch (Exception $e) {
            Log::error('Failed to get collection info from Qdrant: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Count vectors in collection
     *
     * @param array $filter
     * @return int
     */
    public function countVectors(array $filter = []): int
    {
        try {
            $request = [];
            if (!empty($filter)) {
                $request['filter'] = $this->buildFilter($filter);
            }

            $response = $this->client->collections($this->collectionName)->points()->count($request);
            return $response['count'] ?? 0;
        } catch (Exception $e) {
            Log::error('Failed to count vectors in Qdrant: ' . $e->getMessage());
            return 0;
        }
    }
}
