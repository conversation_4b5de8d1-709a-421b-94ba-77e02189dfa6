<?php

namespace App\Services;

use App\Models\TextData;
use App\Models\TextVector;
use App\Models\Vector;
use Illuminate\Support\Facades\DB;
use App\Services\QdrantService;

class VectorService
{
    private QdrantService $qdrantService;

    public function __construct()
    {
        $this->qdrantService = new QdrantService();
    }
    /**
     * Retrieve vectors for a given text.
     *
     * @param string $text
     * @return array
     */
    public function getVectorsForText(string $text): array
    {
        $vectors = DB::table('vectors')
            ->select('id', 'vector')
            ->where('text_id', '=', DB::raw("(SELECT id FROM texts WHERE text = '$text')"))
            ->get()
            ->toArray();

        return array_map(function($vector) {
            return [
                'id' => $vector->id,
                'vector' => json_decode($vector->vector, true)
            ];
        }, $vectors);
    }

    public function getTextsFromIds(array $ids): array
    {
        $texts = TextData::whereIn('id', $ids)->get()->toArray();

        $textsById = []; 

        foreach ($texts as $text) {
            $textsById[$text['id']] = $text['text'];
        }

        $textsOrderedByIds = [];

        foreach ($ids as $id) {
            if (isset($textsById[$id])) {
                $textsOrderedByIds[] = $textsById[$id];
            }
        }

        return $textsOrderedByIds;
    }


    /**
     * Retrieve the text for a given vector ID.
     *
     * @param int $vectorId
     * @return string|null
     */
    public function getTextForVector(int $vectorId): ?string
    {
        $text = DB::table('texts')
            ->select('text')
            ->where('id', '=', DB::raw("(SELECT text_id FROM vectors WHERE id = $vectorId)"))
            ->first();

        return $text ? $text->text : null;
    }

    /**
     * Retrieve the most similar vectors for a given vector using Qdrant.
     *
     * @param array $vector
     * @param int $chatbot_id
     * @param int $limit
     * @return array
     */
    public function getMostSimilarVectors(array $vector, $chatbot_id, int $limit = 10): array
    {
        // Search similar vectors in Qdrant with chatbot filter
        $filter = ['chatbot_id' => $chatbot_id];
        $results = $this->qdrantService->searchSimilarVectors($vector, $limit, $filter);

        $similarVectors = [];
        foreach ($results as $result) {
            $similarVectors[] = [
                'id' => $result['id'],
                'text_id' => $result['payload']['text_id'] ?? null,
                'similarity' => $result['score'],
                'payload' => $result['payload'] ?? []
            ];
        }

        return $similarVectors;
    }

    /**
     * Store vector in Qdrant
     *
     * @param int $id
     * @param array $vector
     * @param array $metadata
     * @return bool
     */
    public function storeVector(int $id, array $vector, array $metadata = []): bool
    {
        $vectors = [
            [
                'id' => $id,
                'vector' => $vector,
                'payload' => $metadata
            ]
        ];

        return $this->qdrantService->upsertVectors($vectors);
    }

    /**
     * Store multiple vectors in Qdrant
     *
     * @param array $vectors
     * @return bool
     */
    public function storeVectors(array $vectors): bool
    {
        return $this->qdrantService->upsertVectors($vectors);
    }

    /**
     * Delete vector from Qdrant
     *
     * @param int $id
     * @return bool
     */
    public function deleteVector(int $id): bool
    {
        return $this->qdrantService->deleteVectors([$id]);
    }

    /**
     * Delete vectors by chatbot ID
     *
     * @param int $chatbot_id
     * @return bool
     */
    public function deleteVectorsByChatbot(int $chatbot_id): bool
    {
        return $this->qdrantService->deleteVectorsByFilter(['chatbot_id' => $chatbot_id]);
    }

    /**
     * Calculate the cosine similarity between two vectors.
     * (Kept for backward compatibility, but Qdrant handles similarity internally)
     *
     * @param array $v1
     * @param array $v2
     * @return float
     */
    private function calculateCosineSimilarity(array $v1, array $v2): float
    {
        $dotProduct = 0;
        $v1Norm = 0;
        $v2Norm = 0;

        foreach ($v1 as $i => $value) {
            $dotProduct += $value * $v2[$i];
            $v1Norm += $value * $value;
            $v2Norm += $v2[$i] * $v2[$i];
        }

        $v1Norm = sqrt($v1Norm);
        $v2Norm = sqrt($v2Norm);

        return $dotProduct / ($v1Norm * $v2Norm);
    }
}
