<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Inertia\Inertia;
use Laravel\Cashier\Exceptions\IncompletePayment;

class SubscriptionController extends Controller
{
    public function subscribe(Request $request, $plan)
    {
        $user = auth()->user();
        
        // Handle different subscription plans
        switch ($plan) {
            case 'free':
                $user->subscription_plan = 'free';
                $user->save();
                return redirect()->route('dashboard')->with('success', 'Free plan selected!');
                break;
                
            case 'pro':
                $priceId = env('STRIPE_PRO_PRICE_ID');
                
                if (empty($priceId)) {
                    return redirect()->back()->with('error', 'Pro plan price ID not configured. Please contact support.');
                }
                
                return $this->createCheckoutSession($request, $priceId, $plan);
                break;
                
            case 'enterprise':
                $priceId = env('STRIPE_ENTERPRISE_PRICE_ID');
                
                if (empty($priceId)) {
                    return redirect()->back()->with('error', 'Enterprise plan price ID not configured. Please contact support.');
                }
                
                return $this->createCheckoutSession($request, $priceId, $plan);
                break;
                
            default:
                return redirect()->back()->with('error', 'Invalid plan selected');
        }
    }

    private function createCheckoutSession(Request $request, $priceId, $planName)
    {
        $user = auth()->user();
        
        try {
            // Simple approach without checking existing subscriptions
            $checkoutSession = $user->newSubscription('default', $priceId)
                ->checkout([
                    'success_url' => route('subscription.success', ['plan' => $planName]),
                    'cancel_url' => route('plan'),
                    'metadata' => [
                        'plan_name' => $planName,
                    ],
                ]);
            
            return Inertia::location($checkoutSession->url);
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Unable to create checkout session: ' . $e->getMessage());
        }
    }
    
    public function success(Request $request, $plan)
    {
        // Don't update subscription_plan here - let webhook handle it
        return redirect()->route('dashboard')->with('success', 'Subscription process completed! Your plan will be updated shortly.');
    }

    // Remove these methods for now if you don't have subscriptions table
    // public function cancel(Request $request) { ... }
    // public function resume(Request $request) { ... }
}