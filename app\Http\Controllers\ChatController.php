<?php

namespace App\Http\Controllers;

use App\Models\Chat;
use Inertia\Inertia;
use App\Models\AiFaq;
use App\Models\Chatbot;
use Illuminate\Support\Str;
use Illuminate\Http\Request;
use App\Services\VectorService;
use OpenAI\Laravel\Facades\OpenAI;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\View;

class ChatController extends Controller
{
    // Removed Pinecone-specific properties as we're now using Qdrant
    // The VectorService will handle all vector operations through Qdrant

    public function __construct()
    {
        // Constructor no longer needs Pinecone configuration
    }

    public function send_message_in_chat(Request $request, $chat_id)
    {
        $input = $request->message;

        // Fetch chat and chatbot details
        $defaultchat = Chat::where('id', $chat_id)->first();
        $selected_chatbot = Chatbot::where('id', $defaultchat->chatbot_id)->first();

        // Retrieve and decode existing chat context
        $messages_no_ai = json_decode($defaultchat->context, true) ?? [];
        $messages_no_ai[] = ['role' => 'user', 'content' => $input];

        try {
            // Convert input into vector using OpenAI embeddings
            $vector = OpenAI::embeddings()->create([
                'model' => 'text-embedding-ada-002',
                'input' => $input,
            ]);

            // Use VectorService to find relevant chunks from Qdrant
            $vectorService = new VectorService();

            $relevantChunks = $vectorService->getMostSimilarVectors(
                $vector['data'][0]['embedding'],
                $selected_chatbot->id,
                4 // Limit relevant chunks to 4
            );

            // Get text data for relevant chunks
            $textIds = array_column($relevantChunks, 'text_id');
            $relevantTexts = $vectorService->getTextsFromIds($textIds);

            // Build context from relevant texts
            $context = implode("\n\n", $relevantTexts);

            // Create system message with context
            $systemMessage = "You are a helpful assistant. Use the following context to answer questions:\n\n" . $context;

            // Prepare messages for OpenAI
            $openaiMessages = [
                ['role' => 'system', 'content' => $systemMessage],
                ['role' => 'user', 'content' => $input]
            ];

            // Get response from OpenAI
            $response = OpenAI::chat()->create([
                'model' => 'gpt-4o',
                'messages' => $openaiMessages,
                'max_tokens' => 1000,
                'temperature' => 0.7,
            ]);

            $message = $response['choices'][0]['message']['content'];

        } catch (\Exception $e) {
            \Log::error('Error in send_message_in_chat: ' . $e->getMessage());
            $message = "I'm sorry, I encountered an error while processing your request. Please try again.";
        }

        $messages_no_ai[] = ['role' => 'assistant', 'content' => $message];

        // Save updated context
        $defaultchat->forceFill([
            'context' => json_encode($messages_no_ai),
        ])->save();

        return ['response' => $message];
    }

    /**
     * Truncate messages to fit within a token limit.
     *
     * @param array $messages
     * @param int $maxTokens
     * @return array
     */
    private function truncateMessages(array $messages, int $maxTokens): array
    {
        $currentTokens = 0;
        $truncatedMessages = [];

        foreach (array_reverse($messages) as $message) {
            $messageTokens = ceil(mb_strlen($message['content']) / 4); // Approximate token count
            if ($currentTokens + $messageTokens > $maxTokens) {
                break;
            }
            $currentTokens += $messageTokens;
            array_unshift($truncatedMessages, $message); // Add back in correct order
        }

        // Always ensure at least the latest user message and system instructions are kept
        if (empty($truncatedMessages)) {
            $truncatedMessages = array_slice($messages, -2); // Keep last two messages
        }

        return $truncatedMessages;
    }

    public function delete_chat($id)
    {
        try {
            $chat = Chat::findOrFail($id);
            $chat->forceFill(['context' => json_encode([])])->save();

            return response()->json(['message' => 'Chat context cleared.']);
        } catch (\Throwable $th) {
            \Log::error('Error in delete_chat: ' . $th->getMessage());
            return response()->json(['error' => 'Failed to delete chat.'], 500);
        }
    }

    public function start_contact(Request $request) {
        $chatbot = Chatbot::findOrFail($request->chatbot_id);
    
        $chat = Chat::create([
            'chatbot_id' => $chatbot->id,
            'user_id' => $chatbot->user_id,
            'random_id' => Str::uuid(),
            'context' => $chatbot->welcome_message,
            'context_added' => false,
            'session_id' => session()->getId(),
            'ip' => $request->ip(),
            'agent' => $request->header('User-Agent'),
            'test_chat' => false,
        ]);
    
        return response()->json(['chat_id' => $chat->id, 'message' => $chatbot->welcome_message]);
    }

    public function getSettings($chatbot_id)
    {
        $chatbot = Chatbot::where('random_id', $chatbot_id)->first();

        if (!$chatbot) {
            return response()->json(['error' => 'Chatbot not found'], 404);
        }

        return response()->json($chatbot);
    }

    public function chatbotIframe($chatbot_id)
    {
        $chatbot = Chatbot::where('random_id', $chatbot_id)->first();

        if (!$chatbot) {
            return response()->json(['error' => 'Chatbot not found'], 404);
        }

        $chat_instance = Chat::where('ip', request()->ip())->where('agent', request()->header('User-Agent'))->first();

        if (!$chat_instance) {
            $chat_instance = Chat::create([
                'chatbot_id' => $chatbot->id, 
                'random_id' => $this->randomNumber(),  
                'context' => json_encode([]),  
                'session_id' => null,
                'ip' =>  request()->ip(),
                'agent' => request()->header('User-Agent'),
                'test_chat' => true, 
                'context_added' => true, 
            ]); 
        }

        $all_messages = null;

        if ($chat_instance->context != []) {
            $all_messages = json_decode($chat_instance->context); 
        }

        return Inertia::render('TryMyAi/MyAiBotIframe', [ 
            'chat' => $chat_instance, 
            'all_messages' => $all_messages, 
            'currently_selected_chatbot' => $chatbot, 
        ]); 

    }


}
