<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\Chatbot;
use Illuminate\Http\Request;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Storage;

class CustomizeChatbotController extends Controller
{
    // Removed Pinecone-specific properties as we're now using Qdrant
    // Vector operations are handled through VectorService

    public function __construct()
    {
        // Constructor no longer needs Pinecone configuration
    }

    public function customize_chatbot(Request $request) {
        $chat_bot = Chatbot::where('user_id', auth()->user()->id)->first();

        $chat_bot->forceFill([
            'chatbot_title' => $request->chatbot_title,
            'chatbot_colour' => $request->chatbot_colour,
        ])->save();

        $user = User::where('id', auth()->user()->id)->first();

        $chatbot = $this->selectedBot();  

        // Before you proceed again - check if 
        if ($chatbot->training_status == false) { 
            $pdf_path = 'trainingfiles/' . auth()->user()->id .'/'. $chatbot->id .'/pdfs';
        
            if (Storage::disk('public')->exists($pdf_path)) {
                $files = Storage::disk('public')->files($pdf_path);

                if (count($files) > 0){

                    $assistant_name = 'assistant' . auth()->user()->id . $chatbot->id ;

                    if (!empty($files)) {
                        foreach ($files as $file) {
                            $filePath = Storage::disk('public')->path($file);
                            $uploadedFile = new UploadedFile(
                                $filePath,
                                basename($file),
                                mime_content_type($filePath),
                                null,
                                true
                            );

                            $uploadedFiles[] = $uploadedFile;
                        }
                    }

                    $this->uploadFilesToAssistant($assistant_name, $uploadedFiles);
                }
            }   

            $txt_path = 'trainingfiles/' . auth()->user()->id .'/'. $chatbot->id .'/linktxtfiles';

            
            if (Storage::disk('public')->exists($txt_path)) {
                $files = Storage::disk('public')->files($txt_path);

                if (count($files) > 0){

                    $assistant_name = 'assistant' . auth()->user()->id . $chatbot->id ;

                    if (!empty($files)) {
                        foreach ($files as $file) {
                            $filePath = Storage::disk('public')->path($file);
                            $uploadedFile = new UploadedFile(
                                $filePath,
                                basename($file),
                                mime_content_type($filePath),
                                null,
                                true
                            );

                            $uploadedFiles[] = $uploadedFile;
                        }
                    }

                    $this->uploadFilesToAssistant($assistant_name, $uploadedFiles);
                }
            } 

        } 

        $new_step = $user->onboarding_step + 1 ;

        $user->forceFill([
            'onboarding_step' => $new_step ,
            'onboarding_done' => true ,
            'currently_selected_chatbot' => $chat_bot->id ,
        ])->save(); 
    }

    //upload files to assistant
    private function uploadFilesToAssistant ($assistant_name , $files) {

        foreach ($files as $file) {
            $metadata = json_encode([
                'uploaded_at' => now()->toDateTimeString(),
                'file_name' => $file->getClientOriginalName(),
            ]);

            $response = Http::withHeaders([
                'Api-Key' => $this->apiKey,
            ])->attach(
                'file',
                file_get_contents($file->getRealPath()),
                $file->getClientOriginalName()
            )->post("https://prod-1-data.ke.pinecone.io/assistant/files/{$assistant_name}?metadata=" . urlencode($metadata));

            if (!$response->successful()) {
                return response()->json(['error' => 'Failed to upload file to assistant', 'details' => $response->json()], 500);
            }else {

                $chatbot = $this->selectedBot();

                $chatbot->forceFill([
                    'training_status' => true,
                ])->save();
            }
        }

    }
}
