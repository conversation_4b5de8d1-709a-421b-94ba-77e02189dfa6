<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Qdrant Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains the configuration for Qdrant vector database.
    | Qdrant is used for storing and searching vector embeddings.
    |
    */

    'host' => env('QDRANT_HOST', 'localhost'),
    'port' => env('QDRANT_PORT', 6333),
    'api_key' => env('QDRANT_API_KEY'),
    'timeout' => env('QDRANT_TIMEOUT', 30),
    
    /*
    |--------------------------------------------------------------------------
    | Collection Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for Qdrant collections used by the application.
    |
    */
    
    'collections' => [
        'vectors' => [
            'name' => env('QDRANT_COLLECTION_NAME', 'chatbot_vectors'),
            'vector_size' => env('QDRANT_VECTOR_SIZE', 1536), // OpenAI text-embedding-ada-002 size
            'distance' => env('QDRANT_DISTANCE_METRIC', 'Cosine'),
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Connection Settings
    |--------------------------------------------------------------------------
    |
    | Additional connection settings for Qdrant client.
    |
    */
    
    'connection' => [
        'verify_ssl' => env('QDRANT_VERIFY_SSL', true),
        'user_agent' => env('QDRANT_USER_AGENT', 'Laravel-Qdrant-Client'),
    ],

];
