<template>
    <div
        class="w-full bg-gray-100 hidden md:flex flex-col min-h-full overflow-y-auto"
    >
        <div class="mt-6 w-full flex justify-center">
            <img src="/img/logos/logo.webp" class="h-14" />
        </div>

        <div v-if="!isRoute('profile.show')" class="w-full px-2 mt-4">
            <Listbox v-model="form.selected_chatbot">
                <div class="relative mt-1">
                    <ListboxButton
                        class="relative w-full cursor-default rounded-lg bg-white py-3 pl-3 text-left border border-gray-300 focus:outline-none focus-visible:border-indigo-500 focus-visible:ring-2 focus-visible:ring-white/75 focus-visible:ring-offset-2 focus-visible:ring-offset-orange-300 text-sm"
                    >
                        <span class="block truncate">{{
                            form.selected_chatbot.chatbot_title
                        }}</span>

                        <span
                            class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2"
                        >
                            <icon
                                name="double_chevron"
                                class="h-4 w-4 text-gray-400"
                                aria-hidden="true"
                            ></icon>
                        </span>
                    </ListboxButton>

                    <transition
                        leave-active-class="transition duration-100 ease-in"
                        leave-from-class="opacity-100"
                        leave-to-class="opacity-0"
                    >
                        <ListboxOptions
                            class="absolute mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black/5 focus:outline-none sm:text-sm"
                        >
                            <ListboxOption
                                v-slot="{ active, selected }"
                                v-for="(bot, index) in $page.props.chatbots"
                                :key="index"
                                :value="bot"
                                as="template"
                            >
                                <li
                                    :class="[
                                        active
                                            ? 'bg-amber-100 text-amber-900'
                                            : 'text-gray-900',
                                        'relative cursor-default select-none py-2 pl-10 pr-4',
                                    ]"
                                >
                                    <span
                                        :class="[
                                            selected
                                                ? 'font-medium'
                                                : 'font-normal',
                                            'block truncate',
                                        ]"
                                        >{{ bot.chatbot_title }}</span
                                    >
                                    <span
                                        v-if="selected"
                                        class="absolute inset-y-0 left-0 flex items-center pl-3 text-amber-600"
                                    >
                                        <icon
                                            name="check"
                                            class="h-5 w-5"
                                            aria-hidden="true"
                                        ></icon>
                                    </span>
                                </li>
                            </ListboxOption>
                        </ListboxOptions>
                    </transition>
                </div>
            </Listbox>
        </div>

        <div class="w-full mt-4 pl-3">
            <h5 class="text-xs text-gray-400 font-semibold">Main Menu</h5>
        </div>

        <div class="flex flex-col items-center px-2 mt-2">
            <Link
                v-for="(link, index) in main_menu_links"
                :key="index"
                :href="route(link.link)"
                :class="{
                    'border-l-4 border-primaryColor/50 bg-primaryColor/20 text-primaryColor':
                        isRoute(link.link),
                }"
                class="border-l-4 border-gray-100 w-full px-3 py-2 hover:bg-primaryColor/50 focus:bg-primaryColor/50 flex items-center rounded-lg mb-1"
            >
                <icon :name="link.icon" class="w-5 h-5 text-gray-700"></icon>
                <span class="text-sm font-semibold text-gray-700 ml-3">
                    {{ link.name }}
                </span>
            </Link>
        </div>

        <div class="w-full mt-4 pl-3">
            <h5 class="text-xs text-gray-400 font-semibold">Chatbot Menu</h5>
        </div>

        <div class="flex flex-col items-center px-2 mt-2">
            <Link
                v-for="(link, index) in chatbot_menu_links"
                :key="index"
                :href="route(link.link)"
                :class="{
                    'border-l-4 border-primaryColor/50 bg-primaryColor/20 text-primaryColor':
                        isRoute(link.link),
                }"
                class="border-l-4 border-gray-100 w-full px-3 py-2 hover:bg-primaryColor/50 focus:bg-primaryColor/50 flex items-center rounded-lg mb-1"
            >
                <icon :name="link.icon" class="w-5 h-5 text-gray-700"></icon>
                <span class="text-sm font-semibold text-gray-700 ml-3">
                    {{ link.name }}
                </span>
            </Link>
        </div>

        <div class="w-full mt-4 pl-3">
            <h5 class="text-xs text-gray-400 font-semibold">Other Links</h5>
        </div>

        <div class="flex flex-col items-center px-2 mt-2">
            <Link
                v-for="(link, index) in others_menu_links"
                :key="index"
                :href="route(link.link)"
                :class="{
                    'border-l-4 border-primaryColor/50 bg-primaryColor/20 text-primaryColor':
                        isRoute(link.link),
                }"
                class="border-l-4 border-gray-100 w-full px-3 py-2 hover:bg-primaryColor/50 focus:bg-primaryColor/50 flex items-center rounded-lg mb-1"
            >
                <icon :name="link.icon" class="w-5 h-5 text-gray-700"></icon>
                <span class="text-sm font-semibold text-gray-700 ml-3">
                    {{ link.name }}
                </span>
            </Link>
        </div>
    </div>
</template>

<script>
import Icon from "@/Components/Global/Icon.vue";
import { Link } from "@inertiajs/vue3";
import {
    Listbox,
    ListboxLabel,
    ListboxButton,
    ListboxOptions,
    ListboxOption,
} from "@headlessui/vue";

export default {
    components: {
        Icon,
        Link,
        Listbox,
        ListboxLabel,
        ListboxButton,
        ListboxOptions,
        ListboxOption,
    },

    data() {
        return {
            main_menu_links: [
                {
                    name: "Dashboard",
                    icon: "home",
                    link: "dashboard",
                },

                {
                    name: "Plan",
                    icon: "plan",
                    link: "plan",
                },

                {
                    name: "Appearance",
                    icon: "appearance",
                    link: "appearance",
                },

                {
                    name: "Try My AI",
                    icon: "try_chat",
                    link: "demo",
                },
            ],

            chatbot_menu_links: [
                {
                    name: "Training Materials",
                    icon: "training_material",
                    link: "training_materials",
                },

                {
                    name: "Question & Answers",
                    icon: "questions_answers",
                    link: "faq_responses",
                },
            ],

            others_menu_links: [
                {
                    name: "Conversations",
                    icon: "conversations",
                    link: "conversations",
                },
                {
                    name: "Captured Contacts",
                    icon: "contacts",
                    link: "contacts",
                },
                {
                    name: "Settings",
                    icon: "settings",
                    link: "profile.show",
                },
            ],

            form: {
                selected_chatbot: this.$page.props.currently_selected_chatbot,
            },
        };
    },

    methods: {
        logout() {
            this.$inertia.post(route("logout"));
        },
    },
};
</script>
