import "./bootstrap";
import "../css/app.css";

import { createApp, h } from "vue";
import { createInertiaApp } from "@inertiajs/vue3";
import { resolvePageComponent } from "laravel-vite-plugin/inertia-helpers";
import { ZiggyVue } from "../../vendor/tightenco/ziggy";
import PrimeVue from "primevue/config";
import Aura from "@primevue/themes/aura";
import ConfirmationService from "primevue/confirmationservice";

const appName = import.meta.env.VITE_APP_NAME || "Laravel";

createInertiaApp({
    title: (title) => `${title} - ${appName}`,
    resolve: (name) =>
        resolvePageComponent(
            `./Pages/${name}.vue`,
            import.meta.glob("./Pages/**/*.vue")
        ),
    setup({ el, App, props, plugin }) {
        return createApp({ render: () => h(App, props) })
            .use(plugin)
            .mixin({
                methods: {
                    isRoute(...routes) {
                        return routes.some((route) =>
                            this.route().current(route)
                        );
                    },
                    isProUser() {
                        return this.$page.props.auth.user?.is_pro === true;
                    },
                },
            })
            .use(ZiggyVue)
            .use(PrimeVue, {
                // existing config
            })
            .use(ConfirmationService)
            .mount(el);
    },
    progress: {
        color: "#a6cc3f",
        showSpinner: true,
    },
});
