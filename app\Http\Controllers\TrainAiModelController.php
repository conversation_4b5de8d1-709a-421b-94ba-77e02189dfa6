<?php

namespace App\Http\Controllers;

use Carbon\Carbon;
use Goutte\Client;
use App\Models\User;
use App\Models\AiFaq;
use App\Models\Chatbot;
use App\Models\TextData;
use App\Models\TextVector;
use Illuminate\Http\Request;
use App\Models\TrainingMaterial;
use Illuminate\Http\UploadedFile;
use OpenAI\Laravel\Facades\OpenAI;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Http;
use Sastrawi\Stemmer\StemmerFactory;
use Illuminate\Support\Facades\Storage;
use Smalot\PdfParser\Parser as PdfParser;
use PhpOffice\PhpWord\IOFactory as WordIOFactory;

class TrainAiModelController extends Controller
{
    // Removed Pinecone-specific properties as we're now using Qdrant
    // Vector operations are handled through VectorService

    public function __construct()
    {
        // Constructor no longer needs Pinecone configuration
    }

    public function submit_training_documents(Request $request) { 
        
        $userId = auth()->user()->id;
        $chatbot = $this->selectedBot();
        $chatbotId = $chatbot->id;

        
        if (!$userId || !$chatbotId) {
            return response()->json(['error' => 'Missing required data'], 400);
        } 

        $files = $request->file('files');

        $assistant_name = 'assistant' . $userId . $chatbotId ;

        if ($chatbot->training_status == false) {

            $this->createAssistant($assistant_name);

            $old_traning_material = TrainingMaterial::where('user_id', $userId)->where('chatbot_id', $chatbotId)->where('material_type', 'File')->get();
    
            foreach ($old_traning_material as $material) {
                $material->delete();
            }
        }

        // Iterate over each uploaded file an create training material
        foreach ($files as $file) {
            $combinedText = ''; 
            $extension = $file->getClientOriginalExtension();
             
            $combinedText .= $this->extractPdfText($file);

            // Store the content in the database as a new TrainingMaterial
            $trainingMaterial = TrainingMaterial::create([
                'user_id' => $userId,
                'chatbot_id' => $chatbotId,
                'random_id' => $this->randomNumber(),
                'material_title' => $file->getClientOriginalName(),
                'material_type' => 'File',
                'charecter_count' => strlen($combinedText),
                'data' => $combinedText,
                'last_trained' => Carbon::now(),
            ]);

        }

        $path = 'trainingfiles/' . $userId .'/'. $chatbotId .'/pdfs';

        // Check if the directory exists and delete it
        if ($chatbot->training_status == false) {
            if (Storage::disk('public')->exists($path)) {
                Storage::disk('public')->deleteDirectory($path);
            }

            // Recreate the directory
            Storage::disk('public')->makeDirectory($path);
        }

        foreach ($files as $file) {
            
            // Save the file locally with its original name
            $fileName = $file->getClientOriginalName();
            
            // Store the file in the 'public' disk
            $file->storeAs($path, $fileName, 'public');
        }

        $this->uploadFilesToAssistant($assistant_name, $files);

        if (auth()->user()->onboarding_done == false){
            $user = User::where('id', auth()->user()->id)->first();
    
            $new_step = $user->onboarding_step + 1 ;
    
            $user->forceFill([
                'onboarding_step' => $new_step
            ])->save(); 
        }
        
        return redirect()->back();
 
    }

    public function submit_training_links(Request $request)
    { 
        $links = $request->input('fetchedLinks');
        $userId = auth()->user()->id; 
        $chatbot = $this->selectedBot();

        $chatbotId = $chatbot->id; 

        if (!is_array($links) || empty($links) || !$userId || !$chatbotId) {
            return response()->json(['error' => 'Missing required data'], 400);
        }

        $assistant_name = 'assistant' . $userId . $chatbotId ;
        
        if (auth()->user()->onboarding_done == false) {
            
            if ($chatbot->training_status == false) {
                $this->createAssistant($assistant_name); 
            }

        } 

        $client = new Client();
        $extractedText = [];
        $fileSavePath = "trainingfiles/{$userId}/{$chatbotId}/linktxtfiles";

        //create txt files directory 
        if (Storage::disk('public')->exists($fileSavePath)) {
            Storage::disk('public')->deleteDirectory($fileSavePath);
        }
        // Recreate the directory
        Storage::disk('public')->makeDirectory($fileSavePath);
        
        foreach ($links as $link) {
            try {
                // Scrape the page
                $crawler = $client->request('GET', $link);

                // Filter out only relevant text content
                $textContent = $crawler->filter('main, article, div.content, section')->each(function ($node) {
                    // Extract text content within paragraphs and headings only
                    return $node->filter('p, h1, h2, h3')->each(function ($subNode) {
                        return trim($subNode->text());
                    });
                });

                // Flatten the array and join content into a single string
                $flattenedContent = array_merge(...$textContent);
                $importantText = array_filter($flattenedContent, fn($text) => !empty($text));
                $finalTextContent = implode(" ", $importantText);

                $newTraininMaterial = TrainingMaterial::create([
                    'user_id' => $userId,
                    'chatbot_id' => $chatbotId,
                    'random_id' => $this->randomNumber(),
                    'material_title' => $link,
                    'material_type' => 'Website',
                    'charecter_count' => strlen($finalTextContent),
                    'last_trained' => Carbon::now(),
                    'data' => $finalTextContent,
                ]);

                 // Store the link info into a text file 
                 $fileName = $newTraininMaterial->random_id . '.txt'; 
                 
                 Storage::disk('public')->put($fileSavePath . '/' . $fileName, $finalTextContent);
            
            
            } catch (\Exception $e) {
                // Handle errors (e.g., connection issues)
                $extractedText[$link] = ['error' => 'Failed to retrieve content.'];
            }
        } 

        if (Storage::disk('public')->exists($fileSavePath)) {
            $files = Storage::disk('public')->files($fileSavePath);

            if (count($files) > 0){

                $assistant_name = 'assistant' . auth()->user()->id . $chatbot->id ;

                if (!empty($files)) {
                    foreach ($files as $file) {
                        $filePath = Storage::disk('public')->path($file);
                        $uploadedFile = new UploadedFile(
                            $filePath,
                            basename($file),
                            mime_content_type($filePath),
                            null,
                            true
                        );

                        $uploadedFiles[] = $uploadedFile;
                    }
                }

                $this->uploadFilesToAssistant($assistant_name, $uploadedFiles);
            }
        } 

        
        if (auth()->user()->onboarding_done == false){
            $user = User::where('id', auth()->user()->id)->first();
        
            $new_step = $user->onboarding_step + 1 ;

            $user->forceFill([
                'onboarding_step' => $new_step
            ])->save();
        }
        return redirect()->back();
       
    }

    //create assistant
    private function createAssistant($assistant_name)
    {
        
        // List all assistants
        $response = Http::withHeaders([
            'Api-Key' => $this->apiKey,
            'X-Pinecone-API-Version' => '2025-01',
        ])->get("https://api.pinecone.io/assistant/assistants");
    

        // Check if the response was successful
        if (!$response->successful()) {
            return response()->json(['error' => 'An error occured!', 'details' => $response->json()], 500);
        }
    
        // Decode the response
        $assistants = $response->json();
    
        // Check if the 'assistants' key exists and contains data
        if (!isset($assistants['assistants']) || !is_array($assistants['assistants'])) {
            return response()->json(['error' => 'Invalid response structure or no assistants found', 'details' => $assistants], 500);
        }
    
        // Check if the assistant exists and delete its files
        $assistantExists = false;

        foreach ($assistants['assistants'] as $assistant) {
            if ($assistant['name'] === $assistant_name) {
                // Fetch the assistant's files
                $filesResponse = Http::withHeaders([
                    'Api-Key' => $this->apiKey,
                    'X-Pinecone-API-Version' => '2025-01',
                ])->get("https://prod-1-data.ke.pinecone.io/assistant/files/{$assistant['name']}");
    
                if (!$filesResponse->successful()) {
                    return response()->json(['error' => 'Failed to fetch assistant files', 'details' => $filesResponse->json()], 500);
                }
    
                $files = $filesResponse->json(); 
    
                // Delete each file
                if (isset($files) && is_array($files)) {

                    foreach ($files['files'] as $fileEntry) { 

                        if (isset($fileEntry['id'])) {
                            $fileId = $fileEntry['id'];

                            $deleteResponse = Http::withHeaders([
                                'Api-Key' => $this->apiKey,
                                'X-Pinecone-API-Version' => '2025-01',
                            ])->delete("https://prod-1-data.ke.pinecone.io/assistant/files/{$assistant['name']}/{$fileId}");
    
                            if (!$deleteResponse->successful()) {
                                return response()->json(['error' => 'Failed to delete file', 'file_id' => $fileId, 'details' => $deleteResponse->json()], 500);
                            }
                        }
                    }
                }
    
                $assistantExists = true;
                break;
            }
        }

        $add_faq = '';

        $frequently_asked_questions = AiFaq::where('user_id', auth()->user()->id)->get();

        if (count($frequently_asked_questions) > 0) {
            $add_faq = "Before Answering any question - AI assistant will first check if the question asked is the one below , if yes - the it give the answer provided :\n\n";
        
            foreach ($frequently_asked_questions as $faq) {
                $add_faq .= "Question: " . $faq->question . "\nAnswer: " . $faq->answer . "\n\n";
            }
        } 
    
        // Create the assistant (if it doesn’t exist or after file cleanup)
        $instructions = "You are an AI assistant designed to provide clear, concise, and accurate answers strictly based on the uploaded documents. Keep responses brief and to the point, ensuring they directly address the question. If the relevant information is not available in the documents, simply state that the answer is not provided. Do not mention document names, internal processing, or system functionality. Your primary goal is to deliver useful, factual responses in the most straightforward manner possible.".

        $add_faq . 
        
        "When someone asks what you help with don't give an answer like this :
        I can assist with providing clear, concise, and accurate answers to your questions based on the information available in the provided documents. All responses will include integrated references in string values using the format '' to ensure transparency and traceability of the information.

        When you don't have information about a certain topic someone is asking about don't say 'The information about X is not provided in the available snippets'. Just say you don't have information on that will be glad to assist about something else (mention company name mentioned in the files).
        
        But rather tell them if they want any information reguarding the company or entity or individual you represent (as seen on the files). When you do this call the entity's name don't say things like : 'I can assist with providing information about the company, entity, or individual you have uploaded documents about. Let me know if you have any specific questions!' - say the name of the entity in your reponses. 
        
        Also never say talk about uploaded documents e.g 'I can assist with providing information about the company, entity, or individual you have uploaded documents about. Let me know if you have any specific questions!' - the person asking the questions doesnt even know documents were uploaded and they don't care.
        
        Be able to answer simple questions like greetings!";
    
        // No longer need to create Pinecone assistant
        // Training data will be processed and stored in Qdrant through VectorService
        \Log::info("Training setup completed for chatbot: {$chatbot->id}");
    
        // return response()->json(['message' => 'Assistant created successfully!', 'assistant' => $createResponse->json()], 201);
    }
     

    // Process files and store vectors in Qdrant
    private function processFilesForVectorStorage($files, $chatbotId, $trainingMaterialId) {
        $vectorService = new VectorService();

        foreach ($files as $file) {
            // Process file content and create embeddings
            $content = file_get_contents($file->getRealPath());

            // Split content into chunks for better vector search
            $chunks = $this->splitTextIntoChunks($content, 1000); // 1000 character chunks

            foreach ($chunks as $index => $chunk) {
                try {
                    // Create embedding for the chunk
                    $embedding = OpenAI::embeddings()->create([
                        'model' => 'text-embedding-ada-002',
                        'input' => $chunk,
                    ]);

                    // Store text data
                    $textData = TextData::create([
                        'text' => $chunk,
                        'chatbot_id' => $chatbotId,
                        'training_material_id' => $trainingMaterialId
                    ]);

                    // Store vector in Qdrant
                    $vectorId = $textData->id;
                    $metadata = [
                        'chatbot_id' => $chatbotId,
                        'training_material_id' => $trainingMaterialId,
                        'text_id' => $textData->id,
                        'file_name' => $file->getClientOriginalName(),
                        'chunk_index' => $index,
                        'uploaded_at' => now()->toDateTimeString(),
                    ];

                    $vectorService->storeVector(
                        $vectorId,
                        $embedding['data'][0]['embedding'],
                        $metadata
                    );

                    // Also store in local database for backup
                    TextVector::create([
                        'vector' => json_encode($embedding['data'][0]['embedding']),
                        'user_id' => auth()->user()->id,
                        'chatbot_id' => $chatbotId,
                        'training_material_id' => $trainingMaterialId,
                        'text_id' => $textData->id
                    ]);

                } catch (\Exception $e) {
                    \Log::error('Error processing file chunk: ' . $e->getMessage());
                }
            }
        }

        // Update chatbot training status
        $chatbot = $this->selectedBot();
        $chatbot->forceFill([
            'training_status' => true,
        ])->save();
    }

    private function splitTextIntoChunks($text, $chunkSize = 1000) {
        $chunks = [];
        $textLength = strlen($text);

        for ($i = 0; $i < $textLength; $i += $chunkSize) {
            $chunks[] = substr($text, $i, $chunkSize);
        }

        return $chunks;
    }

    // Function to extract text from PDF files
    private function extractPdfText($file)
    {
        $parser = new PdfParser();
        $pdf = $parser->parseFile($file->getPathname());
        return $pdf->getText();
    } 
} 
