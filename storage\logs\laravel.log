[2025-06-23 10:01:50] local.INFO: Checkout session completed webhook received {"id":"evt_1Rd7CVR4yt1xFDzX2IYUVQdg","object":"event","api_version":"2025-04-30.basil","created":**********,"data":{"object":{"id":"cs_test_a1cO013RKJei40A3ct636BIPYvbCCAodcDnojUfnETIrcSDoevqe3gVyjo","object":"checkout.session","adaptive_pricing":null,"after_expiration":null,"allow_promotion_codes":null,"amount_subtotal":500,"amount_total":500,"automatic_tax":{"enabled":false,"liability":null,"provider":null,"status":null},"billing_address_collection":null,"cancel_url":"http://localhost:8000/plan","client_reference_id":null,"client_secret":null,"collected_information":{"shipping_details":null},"consent":null,"consent_collection":null,"created":**********,"currency":"usd","currency_conversion":null,"custom_fields":[],"custom_text":{"after_submit":null,"shipping_address":null,"submit":null,"terms_of_service_acceptance":null},"customer":"cus_SX3LyAoliTpGmn","customer_creation":null,"customer_details":{"address":{"city":null,"country":"UA","line1":null,"line2":null,"postal_code":null,"state":null},"email":"<EMAIL>","name":"Serhii","phone":null,"tax_exempt":"none","tax_ids":[]},"customer_email":null,"discounts":[],"expires_at":**********,"invoice":"in_1Rd7CSR4yt1xFDzXiND6bHj5","invoice_creation":null,"livemode":false,"locale":null,"metadata":{"plan_name":"pro"},"mode":"subscription","payment_intent":null,"payment_link":null,"payment_method_collection":"always","payment_method_configuration_details":{"id":"pmc_1RJjbmR4yt1xFDzXlwEFeD8e","parent":null},"payment_method_options":{"card":{"request_three_d_secure":"automatic"}},"payment_method_types":["card","link","cashapp","amazon_pay"],"payment_status":"paid","permissions":null,"phone_number_collection":{"enabled":false},"recovered_from":null,"saved_payment_method_options":{"allow_redisplay_filters":["always"],"payment_method_remove":"disabled","payment_method_save":null},"setup_intent":null,"shipping_address_collection":null,"shipping_cost":null,"shipping_options":[],"status":"complete","submit_type":null,"subscription":"sub_1Rd7CSR4yt1xFDzXdY83RvxF","success_url":"http://localhost:8000/subscription/success/pro","total_details":{"amount_discount":0,"amount_shipping":0,"amount_tax":0},"ui_mode":"hosted","url":null,"wallet_options":null}},"livemode":false,"pending_webhooks":1,"request":{"id":null,"idempotency_key":null},"type":"checkout.session.completed"} 
[2025-06-23 10:01:52] local.INFO: Checkout completed - Updated user 24 subscription to pro  
[2025-06-23 10:01:52] local.INFO: Subscription created webhook received {"id":"evt_1Rd7CVR4yt1xFDzXCtlUR4tX","object":"event","api_version":"2025-04-30.basil","created":1750672925,"data":{"object":{"id":"sub_1Rd7CSR4yt1xFDzXdY83RvxF","object":"subscription","application":null,"application_fee_percent":null,"automatic_tax":{"disabled_reason":null,"enabled":false,"liability":null},"billing_cycle_anchor":**********,"billing_cycle_anchor_config":null,"billing_mode":{"type":"classic"},"billing_thresholds":null,"cancel_at":null,"cancel_at_period_end":false,"canceled_at":null,"cancellation_details":{"comment":null,"feedback":null,"reason":null},"collection_method":"charge_automatically","created":**********,"currency":"usd","customer":"cus_SX3LyAoliTpGmn","days_until_due":null,"default_payment_method":null,"default_source":null,"default_tax_rates":[],"description":null,"discounts":[],"ended_at":null,"invoice_settings":{"account_tax_ids":null,"issuer":{"type":"self"}},"items":{"object":"list","data":[{"id":"si_SYDd5nwKccRX8Z","object":"subscription_item","billing_thresholds":null,"created":**********,"current_period_end":**********,"current_period_start":**********,"discounts":[],"metadata":[],"plan":{"id":"price_1Rauk2R4yt1xFDzX8QKd1XMN","object":"plan","active":true,"amount":500,"amount_decimal":"500","billing_scheme":"per_unit","created":**********,"currency":"usd","interval":"month","interval_count":1,"livemode":false,"metadata":[],"meter":null,"nickname":null,"product":"prod_SVwdp0avHgZEYs","tiers_mode":null,"transform_usage":null,"trial_period_days":null,"usage_type":"licensed"},"price":{"id":"price_1Rauk2R4yt1xFDzX8QKd1XMN","object":"price","active":true,"billing_scheme":"per_unit","created":**********,"currency":"usd","custom_unit_amount":null,"livemode":false,"lookup_key":null,"metadata":[],"nickname":null,"product":"prod_SVwdp0avHgZEYs","recurring":{"interval":"month","interval_count":1,"meter":null,"trial_period_days":null,"usage_type":"licensed"},"tax_behavior":"exclusive","tiers_mode":null,"transform_quantity":null,"type":"recurring","unit_amount":500,"unit_amount_decimal":"500"},"quantity":1,"subscription":"sub_1Rd7CSR4yt1xFDzXdY83RvxF","tax_rates":[]}],"has_more":false,"total_count":1,"url":"/v1/subscription_items?subscription=sub_1Rd7CSR4yt1xFDzXdY83RvxF"},"latest_invoice":"in_1Rd7CSR4yt1xFDzXiND6bHj5","livemode":false,"metadata":{"is_on_session_checkout":"true","type":"default","name":"default"},"next_pending_invoice_item_invoice":null,"on_behalf_of":null,"pause_collection":null,"payment_settings":{"payment_method_options":{"acss_debit":null,"bancontact":null,"card":{"network":null,"request_three_d_secure":"automatic"},"customer_balance":null,"konbini":null,"sepa_debit":null,"us_bank_account":null},"payment_method_types":null,"save_default_payment_method":"off"},"pending_invoice_item_interval":null,"pending_setup_intent":null,"pending_update":null,"plan":{"id":"price_1Rauk2R4yt1xFDzX8QKd1XMN","object":"plan","active":true,"amount":500,"amount_decimal":"500","billing_scheme":"per_unit","created":**********,"currency":"usd","interval":"month","interval_count":1,"livemode":false,"metadata":[],"meter":null,"nickname":null,"product":"prod_SVwdp0avHgZEYs","tiers_mode":null,"transform_usage":null,"trial_period_days":null,"usage_type":"licensed"},"quantity":1,"schedule":null,"start_date":**********,"status":"incomplete","test_clock":null,"transfer_data":null,"trial_end":null,"trial_settings":{"end_behavior":{"missing_payment_method":"create_invoice"}},"trial_start":null}},"livemode":false,"pending_webhooks":1,"request":{"id":null,"idempotency_key":"98c5a654-18e7-4ab9-9822-c75f75141a6e"},"type":"customer.subscription.created"} 
[2025-06-23 10:01:52] local.INFO: Updated user 24 subscription to pro  
[2025-06-23 10:01:52] local.INFO: Subscription updated webhook received {"id":"evt_1Rd7CVR4yt1xFDzXV8Si7AKi","object":"event","api_version":"2025-04-30.basil","created":**********,"data":{"object":{"id":"sub_1Rd7CSR4yt1xFDzXdY83RvxF","object":"subscription","application":null,"application_fee_percent":null,"automatic_tax":{"disabled_reason":null,"enabled":false,"liability":null},"billing_cycle_anchor":**********,"billing_cycle_anchor_config":null,"billing_mode":{"type":"classic"},"billing_thresholds":null,"cancel_at":null,"cancel_at_period_end":false,"canceled_at":null,"cancellation_details":{"comment":null,"feedback":null,"reason":null},"collection_method":"charge_automatically","created":**********,"currency":"usd","customer":"cus_SX3LyAoliTpGmn","days_until_due":null,"default_payment_method":"pm_1Rd7CRR4yt1xFDzXVMeNrWGy","default_source":null,"default_tax_rates":[],"description":null,"discounts":[],"ended_at":null,"invoice_settings":{"account_tax_ids":null,"issuer":{"type":"self"}},"items":{"object":"list","data":[{"id":"si_SYDd5nwKccRX8Z","object":"subscription_item","billing_thresholds":null,"created":**********,"current_period_end":**********,"current_period_start":**********,"discounts":[],"metadata":[],"plan":{"id":"price_1Rauk2R4yt1xFDzX8QKd1XMN","object":"plan","active":true,"amount":500,"amount_decimal":"500","billing_scheme":"per_unit","created":**********,"currency":"usd","interval":"month","interval_count":1,"livemode":false,"metadata":[],"meter":null,"nickname":null,"product":"prod_SVwdp0avHgZEYs","tiers_mode":null,"transform_usage":null,"trial_period_days":null,"usage_type":"licensed"},"price":{"id":"price_1Rauk2R4yt1xFDzX8QKd1XMN","object":"price","active":true,"billing_scheme":"per_unit","created":**********,"currency":"usd","custom_unit_amount":null,"livemode":false,"lookup_key":null,"metadata":[],"nickname":null,"product":"prod_SVwdp0avHgZEYs","recurring":{"interval":"month","interval_count":1,"meter":null,"trial_period_days":null,"usage_type":"licensed"},"tax_behavior":"exclusive","tiers_mode":null,"transform_quantity":null,"type":"recurring","unit_amount":500,"unit_amount_decimal":"500"},"quantity":1,"subscription":"sub_1Rd7CSR4yt1xFDzXdY83RvxF","tax_rates":[]}],"has_more":false,"total_count":1,"url":"/v1/subscription_items?subscription=sub_1Rd7CSR4yt1xFDzXdY83RvxF"},"latest_invoice":"in_1Rd7CSR4yt1xFDzXiND6bHj5","livemode":false,"metadata":{"is_on_session_checkout":"true","type":"default","name":"default"},"next_pending_invoice_item_invoice":null,"on_behalf_of":null,"pause_collection":null,"payment_settings":{"payment_method_options":{"acss_debit":null,"bancontact":null,"card":{"network":null,"request_three_d_secure":"automatic"},"customer_balance":null,"konbini":null,"sepa_debit":null,"us_bank_account":null},"payment_method_types":null,"save_default_payment_method":"off"},"pending_invoice_item_interval":null,"pending_setup_intent":null,"pending_update":null,"plan":{"id":"price_1Rauk2R4yt1xFDzX8QKd1XMN","object":"plan","active":true,"amount":500,"amount_decimal":"500","billing_scheme":"per_unit","created":**********,"currency":"usd","interval":"month","interval_count":1,"livemode":false,"metadata":[],"meter":null,"nickname":null,"product":"prod_SVwdp0avHgZEYs","tiers_mode":null,"transform_usage":null,"trial_period_days":null,"usage_type":"licensed"},"quantity":1,"schedule":null,"start_date":**********,"status":"active","test_clock":null,"transfer_data":null,"trial_end":null,"trial_settings":{"end_behavior":{"missing_payment_method":"create_invoice"}},"trial_start":null},"previous_attributes":{"default_payment_method":null,"status":"incomplete"}},"livemode":false,"pending_webhooks":1,"request":{"id":null,"idempotency_key":"98c5a654-18e7-4ab9-9822-c75f75141a6e"},"type":"customer.subscription.updated"} 
[2025-06-23 10:01:52] local.INFO: Updated user 24 subscription to pro (status: active)  
[2025-06-23 10:01:54] local.INFO: Invoice payment succeeded webhook received {"id":"evt_1Rd7CVR4yt1xFDzXNh4XAIGy","object":"event","api_version":"2025-04-30.basil","created":**********,"data":{"object":{"id":"in_1Rd7CSR4yt1xFDzXiND6bHj5","object":"invoice","account_country":"US","account_name":"Kimana LLC sandbox","account_tax_ids":null,"amount_due":500,"amount_overpaid":0,"amount_paid":500,"amount_remaining":0,"amount_shipping":0,"application":null,"attempt_count":1,"attempted":true,"auto_advance":false,"automatic_tax":{"disabled_reason":null,"enabled":false,"liability":null,"provider":null,"status":null},"automatically_finalizes_at":null,"billing_reason":"subscription_create","collection_method":"charge_automatically","created":**********,"currency":"usd","custom_fields":null,"customer":"cus_SX3LyAoliTpGmn","customer_address":null,"customer_email":"<EMAIL>","customer_name":"Serhii","customer_phone":null,"customer_shipping":null,"customer_tax_exempt":"none","customer_tax_ids":[],"default_payment_method":null,"default_source":null,"default_tax_rates":[],"description":null,"discounts":[],"due_date":null,"effective_at":**********,"ending_balance":0,"footer":null,"from_invoice":null,"hosted_invoice_url":"https://invoice.stripe.com/i/acct_1RJjbGR4yt1xFDzX/test_YWNjdF8xUkpqYkdSNHl0MXhGRHpYLF9TWURkZWlDdkdEaDhjMW5NSVNCdG05OXpFelpFNWZhLDE0MTIxMzcyNw0200EhMjTOjm?s=ap","invoice_pdf":"https://pay.stripe.com/invoice/acct_1RJjbGR4yt1xFDzX/test_YWNjdF8xUkpqYkdSNHl0MXhGRHpYLF9TWURkZWlDdkdEaDhjMW5NSVNCdG05OXpFelpFNWZhLDE0MTIxMzcyNw0200EhMjTOjm/pdf?s=ap","issuer":{"type":"self"},"last_finalization_error":null,"latest_revision":null,"lines":{"object":"list","data":[{"id":"il_1Rd7CSR4yt1xFDzXXGIYnxOt","object":"line_item","amount":500,"currency":"usd","description":"1 × Pro (at $5.00 / month)","discount_amounts":[],"discountable":true,"discounts":[],"invoice":"in_1Rd7CSR4yt1xFDzXiND6bHj5","livemode":false,"metadata":{"is_on_session_checkout":"true","type":"default","name":"default"},"parent":{"invoice_item_details":null,"subscription_item_details":{"invoice_item":null,"proration":false,"proration_details":{"credited_items":"Over 9 levels deep, aborting normalization"},"subscription":"sub_1Rd7CSR4yt1xFDzXdY83RvxF","subscription_item":"si_SYDd5nwKccRX8Z"},"type":"subscription_item_details"},"period":{"end":**********,"start":**********},"pretax_credit_amounts":[],"pricing":{"price_details":{"price":"price_1Rauk2R4yt1xFDzX8QKd1XMN","product":"prod_SVwdp0avHgZEYs"},"type":"price_details","unit_amount_decimal":"500"},"quantity":1,"taxes":[]}],"has_more":false,"total_count":1,"url":"/v1/invoices/in_1Rd7CSR4yt1xFDzXiND6bHj5/lines"},"livemode":false,"metadata":[],"next_payment_attempt":null,"number":"BGFN7R5T-0006","on_behalf_of":null,"parent":{"quote_details":null,"subscription_details":{"metadata":{"is_on_session_checkout":"true","type":"default","name":"default"},"subscription":"sub_1Rd7CSR4yt1xFDzXdY83RvxF"},"type":"subscription_details"},"payment_settings":{"default_mandate":null,"payment_method_options":{"acss_debit":null,"bancontact":null,"card":{"request_three_d_secure":"automatic"},"customer_balance":null,"konbini":null,"sepa_debit":null,"us_bank_account":null},"payment_method_types":null},"period_end":**********,"period_start":**********,"post_payment_credit_notes_amount":0,"pre_payment_credit_notes_amount":0,"receipt_number":null,"rendering":null,"shipping_cost":null,"shipping_details":null,"starting_balance":0,"statement_descriptor":null,"status":"paid","status_transitions":{"finalized_at":**********,"marked_uncollectible_at":null,"paid_at":**********,"voided_at":null},"subtotal":500,"subtotal_excluding_tax":500,"test_clock":null,"total":500,"total_discount_amounts":[],"total_excluding_tax":500,"total_pretax_credit_amounts":[],"total_taxes":[],"webhooks_delivered_at":null}},"livemode":false,"pending_webhooks":1,"request":{"id":null,"idempotency_key":"98c5a654-18e7-4ab9-9822-c75f75141a6e"},"type":"invoice.payment_succeeded"} 
[2025-06-23 10:40:18] local.INFO: Subscription updated webhook received {"id":"evt_1Rd6rUR4yt1xFDzXODQLmhY6","object":"event","api_version":"2025-04-30.basil","created":**********,"data":{"object":{"id":"sub_1Rd6rQR4yt1xFDzX54SV20MX","object":"subscription","application":null,"application_fee_percent":null,"automatic_tax":{"disabled_reason":null,"enabled":false,"liability":null},"billing_cycle_anchor":**********,"billing_cycle_anchor_config":null,"billing_mode":{"type":"classic"},"billing_thresholds":null,"cancel_at":null,"cancel_at_period_end":false,"canceled_at":null,"cancellation_details":{"comment":null,"feedback":null,"reason":null},"collection_method":"charge_automatically","created":**********,"currency":"usd","customer":"cus_SX3LyAoliTpGmn","days_until_due":null,"default_payment_method":"pm_1Rd6rPR4yt1xFDzXBxMyW1XB","default_source":null,"default_tax_rates":[],"description":null,"discounts":[],"ended_at":null,"invoice_settings":{"account_tax_ids":null,"issuer":{"type":"self"}},"items":{"object":"list","data":[{"id":"si_SYDIguaw90APE4","object":"subscription_item","billing_thresholds":null,"created":**********,"current_period_end":**********,"current_period_start":**********,"discounts":[],"metadata":[],"plan":{"id":"price_1Rauk2R4yt1xFDzX8QKd1XMN","object":"plan","active":true,"amount":500,"amount_decimal":"500","billing_scheme":"per_unit","created":**********,"currency":"usd","interval":"month","interval_count":1,"livemode":false,"metadata":[],"meter":null,"nickname":null,"product":"prod_SVwdp0avHgZEYs","tiers_mode":null,"transform_usage":null,"trial_period_days":null,"usage_type":"licensed"},"price":{"id":"price_1Rauk2R4yt1xFDzX8QKd1XMN","object":"price","active":true,"billing_scheme":"per_unit","created":**********,"currency":"usd","custom_unit_amount":null,"livemode":false,"lookup_key":null,"metadata":[],"nickname":null,"product":"prod_SVwdp0avHgZEYs","recurring":{"interval":"month","interval_count":1,"meter":null,"trial_period_days":null,"usage_type":"licensed"},"tax_behavior":"exclusive","tiers_mode":null,"transform_quantity":null,"type":"recurring","unit_amount":500,"unit_amount_decimal":"500"},"quantity":1,"subscription":"sub_1Rd6rQR4yt1xFDzX54SV20MX","tax_rates":[]}],"has_more":false,"total_count":1,"url":"/v1/subscription_items?subscription=sub_1Rd6rQR4yt1xFDzX54SV20MX"},"latest_invoice":"in_1Rd6rQR4yt1xFDzXPPiKGowo","livemode":false,"metadata":{"is_on_session_checkout":"true","type":"default","name":"default"},"next_pending_invoice_item_invoice":null,"on_behalf_of":null,"pause_collection":null,"payment_settings":{"payment_method_options":{"acss_debit":null,"bancontact":null,"card":{"network":null,"request_three_d_secure":"automatic"},"customer_balance":null,"konbini":null,"sepa_debit":null,"us_bank_account":null},"payment_method_types":null,"save_default_payment_method":"off"},"pending_invoice_item_interval":null,"pending_setup_intent":null,"pending_update":null,"plan":{"id":"price_1Rauk2R4yt1xFDzX8QKd1XMN","object":"plan","active":true,"amount":500,"amount_decimal":"500","billing_scheme":"per_unit","created":**********,"currency":"usd","interval":"month","interval_count":1,"livemode":false,"metadata":[],"meter":null,"nickname":null,"product":"prod_SVwdp0avHgZEYs","tiers_mode":null,"transform_usage":null,"trial_period_days":null,"usage_type":"licensed"},"quantity":1,"schedule":null,"start_date":**********,"status":"active","test_clock":null,"transfer_data":null,"trial_end":null,"trial_settings":{"end_behavior":{"missing_payment_method":"create_invoice"}},"trial_start":null},"previous_attributes":{"default_payment_method":null,"status":"incomplete"}},"livemode":false,"pending_webhooks":1,"request":{"id":null,"idempotency_key":"3e7d4114-4cd8-4552-ab6a-ffd16c2dc819"},"type":"customer.subscription.updated"} 
[2025-06-23 10:40:18] local.INFO: Updated user 24 subscription to pro (status: active)  
[2025-06-23 10:40:42] local.INFO: Invoice payment succeeded webhook received {"id":"evt_1Rd6rUR4yt1xFDzXOoKvGUXn","object":"event","api_version":"2025-04-30.basil","created":**********,"data":{"object":{"id":"in_1Rd6rQR4yt1xFDzXPPiKGowo","object":"invoice","account_country":"US","account_name":"Kimana LLC sandbox","account_tax_ids":null,"amount_due":500,"amount_overpaid":0,"amount_paid":500,"amount_remaining":0,"amount_shipping":0,"application":null,"attempt_count":1,"attempted":true,"auto_advance":false,"automatic_tax":{"disabled_reason":null,"enabled":false,"liability":null,"provider":null,"status":null},"automatically_finalizes_at":null,"billing_reason":"subscription_create","collection_method":"charge_automatically","created":**********,"currency":"usd","custom_fields":null,"customer":"cus_SX3LyAoliTpGmn","customer_address":null,"customer_email":"<EMAIL>","customer_name":"Serhii","customer_phone":null,"customer_shipping":null,"customer_tax_exempt":"none","customer_tax_ids":[],"default_payment_method":null,"default_source":null,"default_tax_rates":[],"description":null,"discounts":[],"due_date":null,"effective_at":**********,"ending_balance":0,"footer":null,"from_invoice":null,"hosted_invoice_url":"https://invoice.stripe.com/i/acct_1RJjbGR4yt1xFDzX/test_YWNjdF8xUkpqYkdSNHl0MXhGRHpYLF9TWURJa242SmdhUnBlR0xVT0xGUmtUclJMUXFnMVMwLDE0MTIxMjQyNA0200XHWkTtyo?s=ap","invoice_pdf":"https://pay.stripe.com/invoice/acct_1RJjbGR4yt1xFDzX/test_YWNjdF8xUkpqYkdSNHl0MXhGRHpYLF9TWURJa242SmdhUnBlR0xVT0xGUmtUclJMUXFnMVMwLDE0MTIxMjQyNA0200XHWkTtyo/pdf?s=ap","issuer":{"type":"self"},"last_finalization_error":null,"latest_revision":null,"lines":{"object":"list","data":[{"id":"il_1Rd6rQR4yt1xFDzXUa3uRqRW","object":"line_item","amount":500,"currency":"usd","description":"1 × Pro (at $5.00 / month)","discount_amounts":[],"discountable":true,"discounts":[],"invoice":"in_1Rd6rQR4yt1xFDzXPPiKGowo","livemode":false,"metadata":{"is_on_session_checkout":"true","type":"default","name":"default"},"parent":{"invoice_item_details":null,"subscription_item_details":{"invoice_item":null,"proration":false,"proration_details":{"credited_items":"Over 9 levels deep, aborting normalization"},"subscription":"sub_1Rd6rQR4yt1xFDzX54SV20MX","subscription_item":"si_SYDIguaw90APE4"},"type":"subscription_item_details"},"period":{"end":**********,"start":**********},"pretax_credit_amounts":[],"pricing":{"price_details":{"price":"price_1Rauk2R4yt1xFDzX8QKd1XMN","product":"prod_SVwdp0avHgZEYs"},"type":"price_details","unit_amount_decimal":"500"},"quantity":1,"taxes":[]}],"has_more":false,"total_count":1,"url":"/v1/invoices/in_1Rd6rQR4yt1xFDzXPPiKGowo/lines"},"livemode":false,"metadata":[],"next_payment_attempt":null,"number":"BGFN7R5T-0002","on_behalf_of":null,"parent":{"quote_details":null,"subscription_details":{"metadata":{"is_on_session_checkout":"true","type":"default","name":"default"},"subscription":"sub_1Rd6rQR4yt1xFDzX54SV20MX"},"type":"subscription_details"},"payment_settings":{"default_mandate":null,"payment_method_options":{"acss_debit":null,"bancontact":null,"card":{"request_three_d_secure":"automatic"},"customer_balance":null,"konbini":null,"sepa_debit":null,"us_bank_account":null},"payment_method_types":null},"period_end":**********,"period_start":**********,"post_payment_credit_notes_amount":0,"pre_payment_credit_notes_amount":0,"receipt_number":null,"rendering":null,"shipping_cost":null,"shipping_details":null,"starting_balance":0,"statement_descriptor":null,"status":"paid","status_transitions":{"finalized_at":**********,"marked_uncollectible_at":null,"paid_at":**********,"voided_at":null},"subtotal":500,"subtotal_excluding_tax":500,"test_clock":null,"total":500,"total_discount_amounts":[],"total_excluding_tax":500,"total_pretax_credit_amounts":[],"total_taxes":[],"webhooks_delivered_at":null}},"livemode":false,"pending_webhooks":1,"request":{"id":null,"idempotency_key":"3e7d4114-4cd8-4552-ab6a-ffd16c2dc819"},"type":"invoice.payment_succeeded"} 
[2025-06-23 10:40:47] local.INFO: Checkout session completed webhook received {"id":"evt_1Rd6rUR4yt1xFDzXXedkCphq","object":"event","api_version":"2025-04-30.basil","created":**********,"data":{"object":{"id":"cs_test_a1gzJxYCkCYfdty9IjwvtfiBlS4FdjmP3mLoGKLaoIgegioggWXzAcpFR1","object":"checkout.session","adaptive_pricing":null,"after_expiration":null,"allow_promotion_codes":null,"amount_subtotal":500,"amount_total":500,"automatic_tax":{"enabled":false,"liability":null,"provider":null,"status":null},"billing_address_collection":null,"cancel_url":"http://localhost:8000/plan","client_reference_id":null,"client_secret":null,"collected_information":{"shipping_details":null},"consent":null,"consent_collection":null,"created":**********,"currency":"usd","currency_conversion":null,"custom_fields":[],"custom_text":{"after_submit":null,"shipping_address":null,"submit":null,"terms_of_service_acceptance":null},"customer":"cus_SX3LyAoliTpGmn","customer_creation":null,"customer_details":{"address":{"city":null,"country":"UA","line1":null,"line2":null,"postal_code":null,"state":null},"email":"<EMAIL>","name":"Serhii","phone":null,"tax_exempt":"none","tax_ids":[]},"customer_email":null,"discounts":[],"expires_at":**********,"invoice":"in_1Rd6rQR4yt1xFDzXPPiKGowo","invoice_creation":null,"livemode":false,"locale":null,"metadata":{"plan_name":"pro"},"mode":"subscription","payment_intent":null,"payment_link":null,"payment_method_collection":"always","payment_method_configuration_details":{"id":"pmc_1RJjbmR4yt1xFDzXlwEFeD8e","parent":null},"payment_method_options":{"card":{"request_three_d_secure":"automatic"}},"payment_method_types":["card","link","cashapp","amazon_pay"],"payment_status":"paid","permissions":null,"phone_number_collection":{"enabled":false},"recovered_from":null,"saved_payment_method_options":{"allow_redisplay_filters":["always"],"payment_method_remove":"disabled","payment_method_save":null},"setup_intent":null,"shipping_address_collection":null,"shipping_cost":null,"shipping_options":[],"status":"complete","submit_type":null,"subscription":"sub_1Rd6rQR4yt1xFDzX54SV20MX","success_url":"http://localhost:8000/subscription/success/pro","total_details":{"amount_discount":0,"amount_shipping":0,"amount_tax":0},"ui_mode":"hosted","url":null,"wallet_options":null}},"livemode":false,"pending_webhooks":1,"request":{"id":null,"idempotency_key":null},"type":"checkout.session.completed"} 
[2025-06-23 10:40:49] local.INFO: Checkout completed - Updated user 24 subscription to pro  
[2025-06-23 10:41:23] local.INFO: Subscription created webhook received {"id":"evt_1Rd6rUR4yt1xFDzXS7Kd0fXw","object":"event","api_version":"2025-04-30.basil","created":**********,"data":{"object":{"id":"sub_1Rd6rQR4yt1xFDzX54SV20MX","object":"subscription","application":null,"application_fee_percent":null,"automatic_tax":{"disabled_reason":null,"enabled":false,"liability":null},"billing_cycle_anchor":**********,"billing_cycle_anchor_config":null,"billing_mode":{"type":"classic"},"billing_thresholds":null,"cancel_at":null,"cancel_at_period_end":false,"canceled_at":null,"cancellation_details":{"comment":null,"feedback":null,"reason":null},"collection_method":"charge_automatically","created":**********,"currency":"usd","customer":"cus_SX3LyAoliTpGmn","days_until_due":null,"default_payment_method":null,"default_source":null,"default_tax_rates":[],"description":null,"discounts":[],"ended_at":null,"invoice_settings":{"account_tax_ids":null,"issuer":{"type":"self"}},"items":{"object":"list","data":[{"id":"si_SYDIguaw90APE4","object":"subscription_item","billing_thresholds":null,"created":**********,"current_period_end":**********,"current_period_start":**********,"discounts":[],"metadata":[],"plan":{"id":"price_1Rauk2R4yt1xFDzX8QKd1XMN","object":"plan","active":true,"amount":500,"amount_decimal":"500","billing_scheme":"per_unit","created":**********,"currency":"usd","interval":"month","interval_count":1,"livemode":false,"metadata":[],"meter":null,"nickname":null,"product":"prod_SVwdp0avHgZEYs","tiers_mode":null,"transform_usage":null,"trial_period_days":null,"usage_type":"licensed"},"price":{"id":"price_1Rauk2R4yt1xFDzX8QKd1XMN","object":"price","active":true,"billing_scheme":"per_unit","created":**********,"currency":"usd","custom_unit_amount":null,"livemode":false,"lookup_key":null,"metadata":[],"nickname":null,"product":"prod_SVwdp0avHgZEYs","recurring":{"interval":"month","interval_count":1,"meter":null,"trial_period_days":null,"usage_type":"licensed"},"tax_behavior":"exclusive","tiers_mode":null,"transform_quantity":null,"type":"recurring","unit_amount":500,"unit_amount_decimal":"500"},"quantity":1,"subscription":"sub_1Rd6rQR4yt1xFDzX54SV20MX","tax_rates":[]}],"has_more":false,"total_count":1,"url":"/v1/subscription_items?subscription=sub_1Rd6rQR4yt1xFDzX54SV20MX"},"latest_invoice":"in_1Rd6rQR4yt1xFDzXPPiKGowo","livemode":false,"metadata":{"is_on_session_checkout":"true","type":"default","name":"default"},"next_pending_invoice_item_invoice":null,"on_behalf_of":null,"pause_collection":null,"payment_settings":{"payment_method_options":{"acss_debit":null,"bancontact":null,"card":{"network":null,"request_three_d_secure":"automatic"},"customer_balance":null,"konbini":null,"sepa_debit":null,"us_bank_account":null},"payment_method_types":null,"save_default_payment_method":"off"},"pending_invoice_item_interval":null,"pending_setup_intent":null,"pending_update":null,"plan":{"id":"price_1Rauk2R4yt1xFDzX8QKd1XMN","object":"plan","active":true,"amount":500,"amount_decimal":"500","billing_scheme":"per_unit","created":**********,"currency":"usd","interval":"month","interval_count":1,"livemode":false,"metadata":[],"meter":null,"nickname":null,"product":"prod_SVwdp0avHgZEYs","tiers_mode":null,"transform_usage":null,"trial_period_days":null,"usage_type":"licensed"},"quantity":1,"schedule":null,"start_date":**********,"status":"incomplete","test_clock":null,"transfer_data":null,"trial_end":null,"trial_settings":{"end_behavior":{"missing_payment_method":"create_invoice"}},"trial_start":null}},"livemode":false,"pending_webhooks":1,"request":{"id":null,"idempotency_key":"3e7d4114-4cd8-4552-ab6a-ffd16c2dc819"},"type":"customer.subscription.created"} 
[2025-06-23 10:41:23] local.INFO: Updated user 24 subscription to pro  
[2025-06-23 10:42:42] local.INFO: Subscription created webhook received {"id":"evt_1Rd6uUR4yt1xFDzXoM6UDk0L","object":"event","api_version":"2025-04-30.basil","created":**********,"data":{"object":{"id":"sub_1Rd6uRR4yt1xFDzXfGnt9GAe","object":"subscription","application":null,"application_fee_percent":null,"automatic_tax":{"disabled_reason":null,"enabled":false,"liability":null},"billing_cycle_anchor":**********,"billing_cycle_anchor_config":null,"billing_mode":{"type":"classic"},"billing_thresholds":null,"cancel_at":null,"cancel_at_period_end":false,"canceled_at":null,"cancellation_details":{"comment":null,"feedback":null,"reason":null},"collection_method":"charge_automatically","created":**********,"currency":"usd","customer":"cus_SX3LyAoliTpGmn","days_until_due":null,"default_payment_method":null,"default_source":null,"default_tax_rates":[],"description":null,"discounts":[],"ended_at":null,"invoice_settings":{"account_tax_ids":null,"issuer":{"type":"self"}},"items":{"object":"list","data":[{"id":"si_SYDLSGCYqAivfz","object":"subscription_item","billing_thresholds":null,"created":**********,"current_period_end":**********,"current_period_start":**********,"discounts":[],"metadata":[],"plan":{"id":"price_1Rauk2R4yt1xFDzX8QKd1XMN","object":"plan","active":true,"amount":500,"amount_decimal":"500","billing_scheme":"per_unit","created":**********,"currency":"usd","interval":"month","interval_count":1,"livemode":false,"metadata":[],"meter":null,"nickname":null,"product":"prod_SVwdp0avHgZEYs","tiers_mode":null,"transform_usage":null,"trial_period_days":null,"usage_type":"licensed"},"price":{"id":"price_1Rauk2R4yt1xFDzX8QKd1XMN","object":"price","active":true,"billing_scheme":"per_unit","created":**********,"currency":"usd","custom_unit_amount":null,"livemode":false,"lookup_key":null,"metadata":[],"nickname":null,"product":"prod_SVwdp0avHgZEYs","recurring":{"interval":"month","interval_count":1,"meter":null,"trial_period_days":null,"usage_type":"licensed"},"tax_behavior":"exclusive","tiers_mode":null,"transform_quantity":null,"type":"recurring","unit_amount":500,"unit_amount_decimal":"500"},"quantity":1,"subscription":"sub_1Rd6uRR4yt1xFDzXfGnt9GAe","tax_rates":[]}],"has_more":false,"total_count":1,"url":"/v1/subscription_items?subscription=sub_1Rd6uRR4yt1xFDzXfGnt9GAe"},"latest_invoice":"in_1Rd6uRR4yt1xFDzXp6xaLFKi","livemode":false,"metadata":{"is_on_session_checkout":"true","type":"default","name":"default"},"next_pending_invoice_item_invoice":null,"on_behalf_of":null,"pause_collection":null,"payment_settings":{"payment_method_options":{"acss_debit":null,"bancontact":null,"card":{"network":null,"request_three_d_secure":"automatic"},"customer_balance":null,"konbini":null,"sepa_debit":null,"us_bank_account":null},"payment_method_types":null,"save_default_payment_method":"off"},"pending_invoice_item_interval":null,"pending_setup_intent":null,"pending_update":null,"plan":{"id":"price_1Rauk2R4yt1xFDzX8QKd1XMN","object":"plan","active":true,"amount":500,"amount_decimal":"500","billing_scheme":"per_unit","created":**********,"currency":"usd","interval":"month","interval_count":1,"livemode":false,"metadata":[],"meter":null,"nickname":null,"product":"prod_SVwdp0avHgZEYs","tiers_mode":null,"transform_usage":null,"trial_period_days":null,"usage_type":"licensed"},"quantity":1,"schedule":null,"start_date":**********,"status":"incomplete","test_clock":null,"transfer_data":null,"trial_end":null,"trial_settings":{"end_behavior":{"missing_payment_method":"create_invoice"}},"trial_start":null}},"livemode":false,"pending_webhooks":1,"request":{"id":null,"idempotency_key":"7738331a-f032-4a4d-9da3-68565400bf91"},"type":"customer.subscription.created"} 
[2025-06-23 10:42:42] local.INFO: Updated user 24 subscription to pro  
[2025-06-23 10:43:24] local.INFO: Checkout session completed webhook received {"id":"evt_1Rd6uUR4yt1xFDzXcMTdNeWv","object":"event","api_version":"2025-04-30.basil","created":**********,"data":{"object":{"id":"cs_test_a18e2s6gS2lxjU30mekW1ud6cyNs6aB7PCThVbOYNvrFTUyHJgR11Af8Nf","object":"checkout.session","adaptive_pricing":null,"after_expiration":null,"allow_promotion_codes":null,"amount_subtotal":500,"amount_total":500,"automatic_tax":{"enabled":false,"liability":null,"provider":null,"status":null},"billing_address_collection":null,"cancel_url":"http://localhost:8000/plan","client_reference_id":null,"client_secret":null,"collected_information":{"shipping_details":null},"consent":null,"consent_collection":null,"created":**********,"currency":"usd","currency_conversion":null,"custom_fields":[],"custom_text":{"after_submit":null,"shipping_address":null,"submit":null,"terms_of_service_acceptance":null},"customer":"cus_SX3LyAoliTpGmn","customer_creation":null,"customer_details":{"address":{"city":null,"country":"UA","line1":null,"line2":null,"postal_code":null,"state":null},"email":"<EMAIL>","name":"Serhii","phone":null,"tax_exempt":"none","tax_ids":[]},"customer_email":null,"discounts":[],"expires_at":**********,"invoice":"in_1Rd6uRR4yt1xFDzXp6xaLFKi","invoice_creation":null,"livemode":false,"locale":null,"metadata":{"plan_name":"pro"},"mode":"subscription","payment_intent":null,"payment_link":null,"payment_method_collection":"always","payment_method_configuration_details":{"id":"pmc_1RJjbmR4yt1xFDzXlwEFeD8e","parent":null},"payment_method_options":{"card":{"request_three_d_secure":"automatic"}},"payment_method_types":["card","link","cashapp","amazon_pay"],"payment_status":"paid","permissions":null,"phone_number_collection":{"enabled":false},"recovered_from":null,"saved_payment_method_options":{"allow_redisplay_filters":["always"],"payment_method_remove":"disabled","payment_method_save":null},"setup_intent":null,"shipping_address_collection":null,"shipping_cost":null,"shipping_options":[],"status":"complete","submit_type":null,"subscription":"sub_1Rd6uRR4yt1xFDzXfGnt9GAe","success_url":"http://localhost:8000/subscription/success/pro","total_details":{"amount_discount":0,"amount_shipping":0,"amount_tax":0},"ui_mode":"hosted","url":null,"wallet_options":null}},"livemode":false,"pending_webhooks":1,"request":{"id":null,"idempotency_key":null},"type":"checkout.session.completed"} 
[2025-06-23 10:43:26] local.INFO: Checkout completed - Updated user 24 subscription to pro  
[2025-06-23 10:43:26] local.INFO: Invoice payment succeeded webhook received {"id":"evt_1Rd6uVR4yt1xFDzXCZyP4vHP","object":"event","api_version":"2025-04-30.basil","created":**********,"data":{"object":{"id":"in_1Rd6uRR4yt1xFDzXp6xaLFKi","object":"invoice","account_country":"US","account_name":"Kimana LLC sandbox","account_tax_ids":null,"amount_due":500,"amount_overpaid":0,"amount_paid":500,"amount_remaining":0,"amount_shipping":0,"application":null,"attempt_count":1,"attempted":true,"auto_advance":false,"automatic_tax":{"disabled_reason":null,"enabled":false,"liability":null,"provider":null,"status":null},"automatically_finalizes_at":null,"billing_reason":"subscription_create","collection_method":"charge_automatically","created":**********,"currency":"usd","custom_fields":null,"customer":"cus_SX3LyAoliTpGmn","customer_address":null,"customer_email":"<EMAIL>","customer_name":"Serhii","customer_phone":null,"customer_shipping":null,"customer_tax_exempt":"none","customer_tax_ids":[],"default_payment_method":null,"default_source":null,"default_tax_rates":[],"description":null,"discounts":[],"due_date":null,"effective_at":**********,"ending_balance":0,"footer":null,"from_invoice":null,"hosted_invoice_url":"https://invoice.stripe.com/i/acct_1RJjbGR4yt1xFDzX/test_YWNjdF8xUkpqYkdSNHl0MXhGRHpYLF9TWURMbVJMMTc4c3RkR3VLR1FPeXRhQ2RXOVZMNlFGLDE0MTIxMjYxMQ0200xgCtq0W3?s=ap","invoice_pdf":"https://pay.stripe.com/invoice/acct_1RJjbGR4yt1xFDzX/test_YWNjdF8xUkpqYkdSNHl0MXhGRHpYLF9TWURMbVJMMTc4c3RkR3VLR1FPeXRhQ2RXOVZMNlFGLDE0MTIxMjYxMQ0200xgCtq0W3/pdf?s=ap","issuer":{"type":"self"},"last_finalization_error":null,"latest_revision":null,"lines":{"object":"list","data":[{"id":"il_1Rd6uRR4yt1xFDzXfJKOfWm7","object":"line_item","amount":500,"currency":"usd","description":"1 × Pro (at $5.00 / month)","discount_amounts":[],"discountable":true,"discounts":[],"invoice":"in_1Rd6uRR4yt1xFDzXp6xaLFKi","livemode":false,"metadata":{"is_on_session_checkout":"true","type":"default","name":"default"},"parent":{"invoice_item_details":null,"subscription_item_details":{"invoice_item":null,"proration":false,"proration_details":{"credited_items":"Over 9 levels deep, aborting normalization"},"subscription":"sub_1Rd6uRR4yt1xFDzXfGnt9GAe","subscription_item":"si_SYDLSGCYqAivfz"},"type":"subscription_item_details"},"period":{"end":**********,"start":**********},"pretax_credit_amounts":[],"pricing":{"price_details":{"price":"price_1Rauk2R4yt1xFDzX8QKd1XMN","product":"prod_SVwdp0avHgZEYs"},"type":"price_details","unit_amount_decimal":"500"},"quantity":1,"taxes":[]}],"has_more":false,"total_count":1,"url":"/v1/invoices/in_1Rd6uRR4yt1xFDzXp6xaLFKi/lines"},"livemode":false,"metadata":[],"next_payment_attempt":null,"number":"BGFN7R5T-0003","on_behalf_of":null,"parent":{"quote_details":null,"subscription_details":{"metadata":{"is_on_session_checkout":"true","type":"default","name":"default"},"subscription":"sub_1Rd6uRR4yt1xFDzXfGnt9GAe"},"type":"subscription_details"},"payment_settings":{"default_mandate":null,"payment_method_options":{"acss_debit":null,"bancontact":null,"card":{"request_three_d_secure":"automatic"},"customer_balance":null,"konbini":null,"sepa_debit":null,"us_bank_account":null},"payment_method_types":null},"period_end":**********,"period_start":**********,"post_payment_credit_notes_amount":0,"pre_payment_credit_notes_amount":0,"receipt_number":null,"rendering":null,"shipping_cost":null,"shipping_details":null,"starting_balance":0,"statement_descriptor":null,"status":"paid","status_transitions":{"finalized_at":**********,"marked_uncollectible_at":null,"paid_at":**********,"voided_at":null},"subtotal":500,"subtotal_excluding_tax":500,"test_clock":null,"total":500,"total_discount_amounts":[],"total_excluding_tax":500,"total_pretax_credit_amounts":[],"total_taxes":[],"webhooks_delivered_at":null}},"livemode":false,"pending_webhooks":1,"request":{"id":null,"idempotency_key":"7738331a-f032-4a4d-9da3-68565400bf91"},"type":"invoice.payment_succeeded"} 
[2025-06-23 10:44:27] local.INFO: Subscription updated webhook received {"id":"evt_1Rd6uUR4yt1xFDzXWtzNKuWY","object":"event","api_version":"2025-04-30.basil","created":**********,"data":{"object":{"id":"sub_1Rd6uRR4yt1xFDzXfGnt9GAe","object":"subscription","application":null,"application_fee_percent":null,"automatic_tax":{"disabled_reason":null,"enabled":false,"liability":null},"billing_cycle_anchor":**********,"billing_cycle_anchor_config":null,"billing_mode":{"type":"classic"},"billing_thresholds":null,"cancel_at":null,"cancel_at_period_end":false,"canceled_at":null,"cancellation_details":{"comment":null,"feedback":null,"reason":null},"collection_method":"charge_automatically","created":**********,"currency":"usd","customer":"cus_SX3LyAoliTpGmn","days_until_due":null,"default_payment_method":"pm_1Rd6uQR4yt1xFDzXB93jvijY","default_source":null,"default_tax_rates":[],"description":null,"discounts":[],"ended_at":null,"invoice_settings":{"account_tax_ids":null,"issuer":{"type":"self"}},"items":{"object":"list","data":[{"id":"si_SYDLSGCYqAivfz","object":"subscription_item","billing_thresholds":null,"created":**********,"current_period_end":**********,"current_period_start":**********,"discounts":[],"metadata":[],"plan":{"id":"price_1Rauk2R4yt1xFDzX8QKd1XMN","object":"plan","active":true,"amount":500,"amount_decimal":"500","billing_scheme":"per_unit","created":**********,"currency":"usd","interval":"month","interval_count":1,"livemode":false,"metadata":[],"meter":null,"nickname":null,"product":"prod_SVwdp0avHgZEYs","tiers_mode":null,"transform_usage":null,"trial_period_days":null,"usage_type":"licensed"},"price":{"id":"price_1Rauk2R4yt1xFDzX8QKd1XMN","object":"price","active":true,"billing_scheme":"per_unit","created":**********,"currency":"usd","custom_unit_amount":null,"livemode":false,"lookup_key":null,"metadata":[],"nickname":null,"product":"prod_SVwdp0avHgZEYs","recurring":{"interval":"month","interval_count":1,"meter":null,"trial_period_days":null,"usage_type":"licensed"},"tax_behavior":"exclusive","tiers_mode":null,"transform_quantity":null,"type":"recurring","unit_amount":500,"unit_amount_decimal":"500"},"quantity":1,"subscription":"sub_1Rd6uRR4yt1xFDzXfGnt9GAe","tax_rates":[]}],"has_more":false,"total_count":1,"url":"/v1/subscription_items?subscription=sub_1Rd6uRR4yt1xFDzXfGnt9GAe"},"latest_invoice":"in_1Rd6uRR4yt1xFDzXp6xaLFKi","livemode":false,"metadata":{"is_on_session_checkout":"true","type":"default","name":"default"},"next_pending_invoice_item_invoice":null,"on_behalf_of":null,"pause_collection":null,"payment_settings":{"payment_method_options":{"acss_debit":null,"bancontact":null,"card":{"network":null,"request_three_d_secure":"automatic"},"customer_balance":null,"konbini":null,"sepa_debit":null,"us_bank_account":null},"payment_method_types":null,"save_default_payment_method":"off"},"pending_invoice_item_interval":null,"pending_setup_intent":null,"pending_update":null,"plan":{"id":"price_1Rauk2R4yt1xFDzX8QKd1XMN","object":"plan","active":true,"amount":500,"amount_decimal":"500","billing_scheme":"per_unit","created":**********,"currency":"usd","interval":"month","interval_count":1,"livemode":false,"metadata":[],"meter":null,"nickname":null,"product":"prod_SVwdp0avHgZEYs","tiers_mode":null,"transform_usage":null,"trial_period_days":null,"usage_type":"licensed"},"quantity":1,"schedule":null,"start_date":**********,"status":"active","test_clock":null,"transfer_data":null,"trial_end":null,"trial_settings":{"end_behavior":{"missing_payment_method":"create_invoice"}},"trial_start":null},"previous_attributes":{"default_payment_method":null,"status":"incomplete"}},"livemode":false,"pending_webhooks":1,"request":{"id":null,"idempotency_key":"7738331a-f032-4a4d-9da3-68565400bf91"},"type":"customer.subscription.updated"} 
[2025-06-23 10:44:27] local.INFO: Updated user 24 subscription to pro (status: active)  
[2025-06-23 10:48:15] local.INFO: Subscription updated webhook received {"id":"evt_1Rd700R4yt1xFDzXQcDo3xRf","object":"event","api_version":"2025-04-30.basil","created":**********,"data":{"object":{"id":"sub_1Rd6zxR4yt1xFDzX3lPLH2zi","object":"subscription","application":null,"application_fee_percent":null,"automatic_tax":{"disabled_reason":null,"enabled":false,"liability":null},"billing_cycle_anchor":**********,"billing_cycle_anchor_config":null,"billing_mode":{"type":"classic"},"billing_thresholds":null,"cancel_at":null,"cancel_at_period_end":false,"canceled_at":null,"cancellation_details":{"comment":null,"feedback":null,"reason":null},"collection_method":"charge_automatically","created":**********,"currency":"usd","customer":"cus_SX3LyAoliTpGmn","days_until_due":null,"default_payment_method":"pm_1Rd6zvR4yt1xFDzXaqnGrwSs","default_source":null,"default_tax_rates":[],"description":null,"discounts":[],"ended_at":null,"invoice_settings":{"account_tax_ids":null,"issuer":{"type":"self"}},"items":{"object":"list","data":[{"id":"si_SYDQRbkAiGMDMB","object":"subscription_item","billing_thresholds":null,"created":**********,"current_period_end":**********,"current_period_start":**********,"discounts":[],"metadata":[],"plan":{"id":"price_1Rauk2R4yt1xFDzX8QKd1XMN","object":"plan","active":true,"amount":500,"amount_decimal":"500","billing_scheme":"per_unit","created":**********,"currency":"usd","interval":"month","interval_count":1,"livemode":false,"metadata":[],"meter":null,"nickname":null,"product":"prod_SVwdp0avHgZEYs","tiers_mode":null,"transform_usage":null,"trial_period_days":null,"usage_type":"licensed"},"price":{"id":"price_1Rauk2R4yt1xFDzX8QKd1XMN","object":"price","active":true,"billing_scheme":"per_unit","created":**********,"currency":"usd","custom_unit_amount":null,"livemode":false,"lookup_key":null,"metadata":[],"nickname":null,"product":"prod_SVwdp0avHgZEYs","recurring":{"interval":"month","interval_count":1,"meter":null,"trial_period_days":null,"usage_type":"licensed"},"tax_behavior":"exclusive","tiers_mode":null,"transform_quantity":null,"type":"recurring","unit_amount":500,"unit_amount_decimal":"500"},"quantity":1,"subscription":"sub_1Rd6zxR4yt1xFDzX3lPLH2zi","tax_rates":[]}],"has_more":false,"total_count":1,"url":"/v1/subscription_items?subscription=sub_1Rd6zxR4yt1xFDzX3lPLH2zi"},"latest_invoice":"in_1Rd6zyR4yt1xFDzXx4pGrza7","livemode":false,"metadata":{"is_on_session_checkout":"true","type":"default","name":"default"},"next_pending_invoice_item_invoice":null,"on_behalf_of":null,"pause_collection":null,"payment_settings":{"payment_method_options":{"acss_debit":null,"bancontact":null,"card":{"network":null,"request_three_d_secure":"automatic"},"customer_balance":null,"konbini":null,"sepa_debit":null,"us_bank_account":null},"payment_method_types":null,"save_default_payment_method":"off"},"pending_invoice_item_interval":null,"pending_setup_intent":null,"pending_update":null,"plan":{"id":"price_1Rauk2R4yt1xFDzX8QKd1XMN","object":"plan","active":true,"amount":500,"amount_decimal":"500","billing_scheme":"per_unit","created":**********,"currency":"usd","interval":"month","interval_count":1,"livemode":false,"metadata":[],"meter":null,"nickname":null,"product":"prod_SVwdp0avHgZEYs","tiers_mode":null,"transform_usage":null,"trial_period_days":null,"usage_type":"licensed"},"quantity":1,"schedule":null,"start_date":**********,"status":"active","test_clock":null,"transfer_data":null,"trial_end":null,"trial_settings":{"end_behavior":{"missing_payment_method":"create_invoice"}},"trial_start":null},"previous_attributes":{"default_payment_method":null,"status":"incomplete"}},"livemode":false,"pending_webhooks":1,"request":{"id":null,"idempotency_key":"********-c163-479f-a688-bff7e34e7a8a"},"type":"customer.subscription.updated"} 
[2025-06-23 10:48:15] local.INFO: Updated user 24 subscription to pro (status: active)  
[2025-06-23 10:48:28] local.INFO: Checkout session completed webhook received {"id":"evt_1Rd700R4yt1xFDzX6LVGydMK","object":"event","api_version":"2025-04-30.basil","created":**********,"data":{"object":{"id":"cs_test_a1n1BbsiK4fwuMVwOMebZaZf5NG2DAo0mOjYa5EvP4eIrrBZwqaX8o4x3L","object":"checkout.session","adaptive_pricing":null,"after_expiration":null,"allow_promotion_codes":null,"amount_subtotal":500,"amount_total":500,"automatic_tax":{"enabled":false,"liability":null,"provider":null,"status":null},"billing_address_collection":null,"cancel_url":"http://localhost:8000/plan","client_reference_id":null,"client_secret":null,"collected_information":{"shipping_details":null},"consent":null,"consent_collection":null,"created":**********,"currency":"usd","currency_conversion":null,"custom_fields":[],"custom_text":{"after_submit":null,"shipping_address":null,"submit":null,"terms_of_service_acceptance":null},"customer":"cus_SX3LyAoliTpGmn","customer_creation":null,"customer_details":{"address":{"city":null,"country":"UA","line1":null,"line2":null,"postal_code":null,"state":null},"email":"<EMAIL>","name":"Serhii","phone":null,"tax_exempt":"none","tax_ids":[]},"customer_email":null,"discounts":[],"expires_at":**********,"invoice":"in_1Rd6zyR4yt1xFDzXx4pGrza7","invoice_creation":null,"livemode":false,"locale":null,"metadata":{"plan_name":"pro"},"mode":"subscription","payment_intent":null,"payment_link":null,"payment_method_collection":"always","payment_method_configuration_details":{"id":"pmc_1RJjbmR4yt1xFDzXlwEFeD8e","parent":null},"payment_method_options":{"card":{"request_three_d_secure":"automatic"}},"payment_method_types":["card","link","cashapp","amazon_pay"],"payment_status":"paid","permissions":null,"phone_number_collection":{"enabled":false},"recovered_from":null,"saved_payment_method_options":{"allow_redisplay_filters":["always"],"payment_method_remove":"disabled","payment_method_save":null},"setup_intent":null,"shipping_address_collection":null,"shipping_cost":null,"shipping_options":[],"status":"complete","submit_type":null,"subscription":"sub_1Rd6zxR4yt1xFDzX3lPLH2zi","success_url":"http://localhost:8000/subscription/success/pro","total_details":{"amount_discount":0,"amount_shipping":0,"amount_tax":0},"ui_mode":"hosted","url":null,"wallet_options":null}},"livemode":false,"pending_webhooks":1,"request":{"id":null,"idempotency_key":null},"type":"checkout.session.completed"} 
[2025-06-23 10:48:29] local.INFO: Checkout completed - Updated user 24 subscription to pro  
[2025-06-23 10:48:39] local.INFO: Invoice payment succeeded webhook received {"id":"evt_1Rd701R4yt1xFDzXG8Lcu11s","object":"event","api_version":"2025-04-30.basil","created":**********,"data":{"object":{"id":"in_1Rd6zyR4yt1xFDzXx4pGrza7","object":"invoice","account_country":"US","account_name":"Kimana LLC sandbox","account_tax_ids":null,"amount_due":500,"amount_overpaid":0,"amount_paid":500,"amount_remaining":0,"amount_shipping":0,"application":null,"attempt_count":1,"attempted":true,"auto_advance":false,"automatic_tax":{"disabled_reason":null,"enabled":false,"liability":null,"provider":null,"status":null},"automatically_finalizes_at":null,"billing_reason":"subscription_create","collection_method":"charge_automatically","created":**********,"currency":"usd","custom_fields":null,"customer":"cus_SX3LyAoliTpGmn","customer_address":null,"customer_email":"<EMAIL>","customer_name":"Serhii","customer_phone":null,"customer_shipping":null,"customer_tax_exempt":"none","customer_tax_ids":[],"default_payment_method":null,"default_source":null,"default_tax_rates":[],"description":null,"discounts":[],"due_date":null,"effective_at":**********,"ending_balance":0,"footer":null,"from_invoice":null,"hosted_invoice_url":"https://invoice.stripe.com/i/acct_1RJjbGR4yt1xFDzX/test_YWNjdF8xUkpqYkdSNHl0MXhGRHpYLF9TWURRMVZYSFFBZEV0aTVjQkNlU3g3ajJ3UWV5aFczLDE0MTIxMjk1Mw020008Vcb1K0?s=ap","invoice_pdf":"https://pay.stripe.com/invoice/acct_1RJjbGR4yt1xFDzX/test_YWNjdF8xUkpqYkdSNHl0MXhGRHpYLF9TWURRMVZYSFFBZEV0aTVjQkNlU3g3ajJ3UWV5aFczLDE0MTIxMjk1Mw020008Vcb1K0/pdf?s=ap","issuer":{"type":"self"},"last_finalization_error":null,"latest_revision":null,"lines":{"object":"list","data":[{"id":"il_1Rd6zyR4yt1xFDzXZK33mabO","object":"line_item","amount":500,"currency":"usd","description":"1 × Pro (at $5.00 / month)","discount_amounts":[],"discountable":true,"discounts":[],"invoice":"in_1Rd6zyR4yt1xFDzXx4pGrza7","livemode":false,"metadata":{"is_on_session_checkout":"true","type":"default","name":"default"},"parent":{"invoice_item_details":null,"subscription_item_details":{"invoice_item":null,"proration":false,"proration_details":{"credited_items":"Over 9 levels deep, aborting normalization"},"subscription":"sub_1Rd6zxR4yt1xFDzX3lPLH2zi","subscription_item":"si_SYDQRbkAiGMDMB"},"type":"subscription_item_details"},"period":{"end":**********,"start":**********},"pretax_credit_amounts":[],"pricing":{"price_details":{"price":"price_1Rauk2R4yt1xFDzX8QKd1XMN","product":"prod_SVwdp0avHgZEYs"},"type":"price_details","unit_amount_decimal":"500"},"quantity":1,"taxes":[]}],"has_more":false,"total_count":1,"url":"/v1/invoices/in_1Rd6zyR4yt1xFDzXx4pGrza7/lines"},"livemode":false,"metadata":[],"next_payment_attempt":null,"number":"BGFN7R5T-0004","on_behalf_of":null,"parent":{"quote_details":null,"subscription_details":{"metadata":{"is_on_session_checkout":"true","type":"default","name":"default"},"subscription":"sub_1Rd6zxR4yt1xFDzX3lPLH2zi"},"type":"subscription_details"},"payment_settings":{"default_mandate":null,"payment_method_options":{"acss_debit":null,"bancontact":null,"card":{"request_three_d_secure":"automatic"},"customer_balance":null,"konbini":null,"sepa_debit":null,"us_bank_account":null},"payment_method_types":null},"period_end":**********,"period_start":**********,"post_payment_credit_notes_amount":0,"pre_payment_credit_notes_amount":0,"receipt_number":null,"rendering":null,"shipping_cost":null,"shipping_details":null,"starting_balance":0,"statement_descriptor":null,"status":"paid","status_transitions":{"finalized_at":**********,"marked_uncollectible_at":null,"paid_at":**********,"voided_at":null},"subtotal":500,"subtotal_excluding_tax":500,"test_clock":null,"total":500,"total_discount_amounts":[],"total_excluding_tax":500,"total_pretax_credit_amounts":[],"total_taxes":[],"webhooks_delivered_at":null}},"livemode":false,"pending_webhooks":1,"request":{"id":null,"idempotency_key":"********-c163-479f-a688-bff7e34e7a8a"},"type":"invoice.payment_succeeded"} 
[2025-06-23 10:49:20] local.INFO: Subscription created webhook received {"id":"evt_1Rd700R4yt1xFDzX2DUuy5tO","object":"event","api_version":"2025-04-30.basil","created":**********,"data":{"object":{"id":"sub_1Rd6zxR4yt1xFDzX3lPLH2zi","object":"subscription","application":null,"application_fee_percent":null,"automatic_tax":{"disabled_reason":null,"enabled":false,"liability":null},"billing_cycle_anchor":**********,"billing_cycle_anchor_config":null,"billing_mode":{"type":"classic"},"billing_thresholds":null,"cancel_at":null,"cancel_at_period_end":false,"canceled_at":null,"cancellation_details":{"comment":null,"feedback":null,"reason":null},"collection_method":"charge_automatically","created":**********,"currency":"usd","customer":"cus_SX3LyAoliTpGmn","days_until_due":null,"default_payment_method":null,"default_source":null,"default_tax_rates":[],"description":null,"discounts":[],"ended_at":null,"invoice_settings":{"account_tax_ids":null,"issuer":{"type":"self"}},"items":{"object":"list","data":[{"id":"si_SYDQRbkAiGMDMB","object":"subscription_item","billing_thresholds":null,"created":**********,"current_period_end":**********,"current_period_start":**********,"discounts":[],"metadata":[],"plan":{"id":"price_1Rauk2R4yt1xFDzX8QKd1XMN","object":"plan","active":true,"amount":500,"amount_decimal":"500","billing_scheme":"per_unit","created":**********,"currency":"usd","interval":"month","interval_count":1,"livemode":false,"metadata":[],"meter":null,"nickname":null,"product":"prod_SVwdp0avHgZEYs","tiers_mode":null,"transform_usage":null,"trial_period_days":null,"usage_type":"licensed"},"price":{"id":"price_1Rauk2R4yt1xFDzX8QKd1XMN","object":"price","active":true,"billing_scheme":"per_unit","created":**********,"currency":"usd","custom_unit_amount":null,"livemode":false,"lookup_key":null,"metadata":[],"nickname":null,"product":"prod_SVwdp0avHgZEYs","recurring":{"interval":"month","interval_count":1,"meter":null,"trial_period_days":null,"usage_type":"licensed"},"tax_behavior":"exclusive","tiers_mode":null,"transform_quantity":null,"type":"recurring","unit_amount":500,"unit_amount_decimal":"500"},"quantity":1,"subscription":"sub_1Rd6zxR4yt1xFDzX3lPLH2zi","tax_rates":[]}],"has_more":false,"total_count":1,"url":"/v1/subscription_items?subscription=sub_1Rd6zxR4yt1xFDzX3lPLH2zi"},"latest_invoice":"in_1Rd6zyR4yt1xFDzXx4pGrza7","livemode":false,"metadata":{"is_on_session_checkout":"true","type":"default","name":"default"},"next_pending_invoice_item_invoice":null,"on_behalf_of":null,"pause_collection":null,"payment_settings":{"payment_method_options":{"acss_debit":null,"bancontact":null,"card":{"network":null,"request_three_d_secure":"automatic"},"customer_balance":null,"konbini":null,"sepa_debit":null,"us_bank_account":null},"payment_method_types":null,"save_default_payment_method":"off"},"pending_invoice_item_interval":null,"pending_setup_intent":null,"pending_update":null,"plan":{"id":"price_1Rauk2R4yt1xFDzX8QKd1XMN","object":"plan","active":true,"amount":500,"amount_decimal":"500","billing_scheme":"per_unit","created":**********,"currency":"usd","interval":"month","interval_count":1,"livemode":false,"metadata":[],"meter":null,"nickname":null,"product":"prod_SVwdp0avHgZEYs","tiers_mode":null,"transform_usage":null,"trial_period_days":null,"usage_type":"licensed"},"quantity":1,"schedule":null,"start_date":**********,"status":"incomplete","test_clock":null,"transfer_data":null,"trial_end":null,"trial_settings":{"end_behavior":{"missing_payment_method":"create_invoice"}},"trial_start":null}},"livemode":false,"pending_webhooks":1,"request":{"id":null,"idempotency_key":"********-c163-479f-a688-bff7e34e7a8a"},"type":"customer.subscription.created"} 
[2025-06-23 10:49:20] local.INFO: Updated user 24 subscription to pro  
[2025-06-23 10:50:24] local.INFO: Invoice payment succeeded webhook received {"id":"evt_1Rd724R4yt1xFDzXbCgchT0n","object":"event","api_version":"2025-04-30.basil","created":**********,"data":{"object":{"id":"in_1Rd720R4yt1xFDzXAzwsMOHa","object":"invoice","account_country":"US","account_name":"Kimana LLC sandbox","account_tax_ids":null,"amount_due":500,"amount_overpaid":0,"amount_paid":500,"amount_remaining":0,"amount_shipping":0,"application":null,"attempt_count":1,"attempted":true,"auto_advance":false,"automatic_tax":{"disabled_reason":null,"enabled":false,"liability":null,"provider":null,"status":null},"automatically_finalizes_at":null,"billing_reason":"subscription_create","collection_method":"charge_automatically","created":**********,"currency":"usd","custom_fields":null,"customer":"cus_SX3LyAoliTpGmn","customer_address":null,"customer_email":"<EMAIL>","customer_name":"Serhii","customer_phone":null,"customer_shipping":null,"customer_tax_exempt":"none","customer_tax_ids":[],"default_payment_method":null,"default_source":null,"default_tax_rates":[],"description":null,"discounts":[],"due_date":null,"effective_at":**********,"ending_balance":0,"footer":null,"from_invoice":null,"hosted_invoice_url":"https://invoice.stripe.com/i/acct_1RJjbGR4yt1xFDzX/test_YWNjdF8xUkpqYkdSNHl0MXhGRHpYLF9TWURTR3RNYXpoWDFjOVZVNVJ5TGlDcENwZWtHdW96LDE0MTIxMzA4MA020043U0qyEh?s=ap","invoice_pdf":"https://pay.stripe.com/invoice/acct_1RJjbGR4yt1xFDzX/test_YWNjdF8xUkpqYkdSNHl0MXhGRHpYLF9TWURTR3RNYXpoWDFjOVZVNVJ5TGlDcENwZWtHdW96LDE0MTIxMzA4MA020043U0qyEh/pdf?s=ap","issuer":{"type":"self"},"last_finalization_error":null,"latest_revision":null,"lines":{"object":"list","data":[{"id":"il_1Rd720R4yt1xFDzXO9vgHgVE","object":"line_item","amount":500,"currency":"usd","description":"1 × Pro (at $5.00 / month)","discount_amounts":[],"discountable":true,"discounts":[],"invoice":"in_1Rd720R4yt1xFDzXAzwsMOHa","livemode":false,"metadata":{"is_on_session_checkout":"true","type":"default","name":"default"},"parent":{"invoice_item_details":null,"subscription_item_details":{"invoice_item":null,"proration":false,"proration_details":{"credited_items":"Over 9 levels deep, aborting normalization"},"subscription":"sub_1Rd720R4yt1xFDzXeW6GBXDs","subscription_item":"si_SYDS9O7nbBGUP0"},"type":"subscription_item_details"},"period":{"end":1753264276,"start":**********},"pretax_credit_amounts":[],"pricing":{"price_details":{"price":"price_1Rauk2R4yt1xFDzX8QKd1XMN","product":"prod_SVwdp0avHgZEYs"},"type":"price_details","unit_amount_decimal":"500"},"quantity":1,"taxes":[]}],"has_more":false,"total_count":1,"url":"/v1/invoices/in_1Rd720R4yt1xFDzXAzwsMOHa/lines"},"livemode":false,"metadata":[],"next_payment_attempt":null,"number":"BGFN7R5T-0005","on_behalf_of":null,"parent":{"quote_details":null,"subscription_details":{"metadata":{"is_on_session_checkout":"true","type":"default","name":"default"},"subscription":"sub_1Rd720R4yt1xFDzXeW6GBXDs"},"type":"subscription_details"},"payment_settings":{"default_mandate":null,"payment_method_options":{"acss_debit":null,"bancontact":null,"card":{"request_three_d_secure":"automatic"},"customer_balance":null,"konbini":null,"sepa_debit":null,"us_bank_account":null},"payment_method_types":null},"period_end":**********,"period_start":**********,"post_payment_credit_notes_amount":0,"pre_payment_credit_notes_amount":0,"receipt_number":null,"rendering":null,"shipping_cost":null,"shipping_details":null,"starting_balance":0,"statement_descriptor":null,"status":"paid","status_transitions":{"finalized_at":**********,"marked_uncollectible_at":null,"paid_at":**********,"voided_at":null},"subtotal":500,"subtotal_excluding_tax":500,"test_clock":null,"total":500,"total_discount_amounts":[],"total_excluding_tax":500,"total_pretax_credit_amounts":[],"total_taxes":[],"webhooks_delivered_at":null}},"livemode":false,"pending_webhooks":1,"request":{"id":null,"idempotency_key":"bcd1625c-6bcd-45ae-a4c3-777ba05baa00"},"type":"invoice.payment_succeeded"} 
[2025-06-23 12:23:01] local.INFO: Subscription created webhook received {"id":"evt_1Rd9PAR4yt1xFDzXCu6umptc","object":"event","api_version":"2025-04-30.basil","created":**********,"data":{"object":{"id":"sub_1Rd9P7R4yt1xFDzXQ2Oieop9","object":"subscription","application":null,"application_fee_percent":null,"automatic_tax":{"disabled_reason":null,"enabled":false,"liability":null},"billing_cycle_anchor":**********,"billing_cycle_anchor_config":null,"billing_mode":{"type":"classic"},"billing_thresholds":null,"cancel_at":null,"cancel_at_period_end":false,"canceled_at":null,"cancellation_details":{"comment":null,"feedback":null,"reason":null},"collection_method":"charge_automatically","created":**********,"currency":"usd","customer":"cus_SX3LyAoliTpGmn","days_until_due":null,"default_payment_method":null,"default_source":null,"default_tax_rates":[],"description":null,"discounts":[],"ended_at":null,"invoice_settings":{"account_tax_ids":null,"issuer":{"type":"self"}},"items":{"object":"list","data":[{"id":"si_SYFu9yJfP8y0wV","object":"subscription_item","billing_thresholds":null,"created":**********,"current_period_end":**********,"current_period_start":**********,"discounts":[],"metadata":[],"plan":{"id":"price_1Rauk2R4yt1xFDzX8QKd1XMN","object":"plan","active":true,"amount":500,"amount_decimal":"500","billing_scheme":"per_unit","created":**********,"currency":"usd","interval":"month","interval_count":1,"livemode":false,"metadata":[],"meter":null,"nickname":null,"product":"prod_SVwdp0avHgZEYs","tiers_mode":null,"transform_usage":null,"trial_period_days":null,"usage_type":"licensed"},"price":{"id":"price_1Rauk2R4yt1xFDzX8QKd1XMN","object":"price","active":true,"billing_scheme":"per_unit","created":**********,"currency":"usd","custom_unit_amount":null,"livemode":false,"lookup_key":null,"metadata":[],"nickname":null,"product":"prod_SVwdp0avHgZEYs","recurring":{"interval":"month","interval_count":1,"meter":null,"trial_period_days":null,"usage_type":"licensed"},"tax_behavior":"exclusive","tiers_mode":null,"transform_quantity":null,"type":"recurring","unit_amount":500,"unit_amount_decimal":"500"},"quantity":1,"subscription":"sub_1Rd9P7R4yt1xFDzXQ2Oieop9","tax_rates":[]}],"has_more":false,"total_count":1,"url":"/v1/subscription_items?subscription=sub_1Rd9P7R4yt1xFDzXQ2Oieop9"},"latest_invoice":"in_1Rd9P7R4yt1xFDzXBIulPnjG","livemode":false,"metadata":{"is_on_session_checkout":"true","type":"default","name":"default"},"next_pending_invoice_item_invoice":null,"on_behalf_of":null,"pause_collection":null,"payment_settings":{"payment_method_options":{"acss_debit":null,"bancontact":null,"card":{"network":null,"request_three_d_secure":"automatic"},"customer_balance":null,"konbini":null,"sepa_debit":null,"us_bank_account":null},"payment_method_types":null,"save_default_payment_method":"off"},"pending_invoice_item_interval":null,"pending_setup_intent":null,"pending_update":null,"plan":{"id":"price_1Rauk2R4yt1xFDzX8QKd1XMN","object":"plan","active":true,"amount":500,"amount_decimal":"500","billing_scheme":"per_unit","created":**********,"currency":"usd","interval":"month","interval_count":1,"livemode":false,"metadata":[],"meter":null,"nickname":null,"product":"prod_SVwdp0avHgZEYs","tiers_mode":null,"transform_usage":null,"trial_period_days":null,"usage_type":"licensed"},"quantity":1,"schedule":null,"start_date":**********,"status":"incomplete","test_clock":null,"transfer_data":null,"trial_end":null,"trial_settings":{"end_behavior":{"missing_payment_method":"create_invoice"}},"trial_start":null}},"livemode":false,"pending_webhooks":1,"request":{"id":null,"idempotency_key":"33c1781d-39f2-4ce4-a500-3dd11b636d4d"},"type":"customer.subscription.created"} 
[2025-06-23 12:23:01] local.INFO: Updated user 24 subscription to pro  
[2025-06-23 12:23:02] local.INFO: Subscription updated webhook received {"id":"evt_1Rd9PAR4yt1xFDzXJursqCi7","object":"event","api_version":"2025-04-30.basil","created":**********,"data":{"object":{"id":"sub_1Rd9P7R4yt1xFDzXQ2Oieop9","object":"subscription","application":null,"application_fee_percent":null,"automatic_tax":{"disabled_reason":null,"enabled":false,"liability":null},"billing_cycle_anchor":**********,"billing_cycle_anchor_config":null,"billing_mode":{"type":"classic"},"billing_thresholds":null,"cancel_at":null,"cancel_at_period_end":false,"canceled_at":null,"cancellation_details":{"comment":null,"feedback":null,"reason":null},"collection_method":"charge_automatically","created":**********,"currency":"usd","customer":"cus_SX3LyAoliTpGmn","days_until_due":null,"default_payment_method":"pm_1Rd9P6R4yt1xFDzXFjfmFvJG","default_source":null,"default_tax_rates":[],"description":null,"discounts":[],"ended_at":null,"invoice_settings":{"account_tax_ids":null,"issuer":{"type":"self"}},"items":{"object":"list","data":[{"id":"si_SYFu9yJfP8y0wV","object":"subscription_item","billing_thresholds":null,"created":**********,"current_period_end":**********,"current_period_start":**********,"discounts":[],"metadata":[],"plan":{"id":"price_1Rauk2R4yt1xFDzX8QKd1XMN","object":"plan","active":true,"amount":500,"amount_decimal":"500","billing_scheme":"per_unit","created":**********,"currency":"usd","interval":"month","interval_count":1,"livemode":false,"metadata":[],"meter":null,"nickname":null,"product":"prod_SVwdp0avHgZEYs","tiers_mode":null,"transform_usage":null,"trial_period_days":null,"usage_type":"licensed"},"price":{"id":"price_1Rauk2R4yt1xFDzX8QKd1XMN","object":"price","active":true,"billing_scheme":"per_unit","created":**********,"currency":"usd","custom_unit_amount":null,"livemode":false,"lookup_key":null,"metadata":[],"nickname":null,"product":"prod_SVwdp0avHgZEYs","recurring":{"interval":"month","interval_count":1,"meter":null,"trial_period_days":null,"usage_type":"licensed"},"tax_behavior":"exclusive","tiers_mode":null,"transform_quantity":null,"type":"recurring","unit_amount":500,"unit_amount_decimal":"500"},"quantity":1,"subscription":"sub_1Rd9P7R4yt1xFDzXQ2Oieop9","tax_rates":[]}],"has_more":false,"total_count":1,"url":"/v1/subscription_items?subscription=sub_1Rd9P7R4yt1xFDzXQ2Oieop9"},"latest_invoice":"in_1Rd9P7R4yt1xFDzXBIulPnjG","livemode":false,"metadata":{"is_on_session_checkout":"true","type":"default","name":"default"},"next_pending_invoice_item_invoice":null,"on_behalf_of":null,"pause_collection":null,"payment_settings":{"payment_method_options":{"acss_debit":null,"bancontact":null,"card":{"network":null,"request_three_d_secure":"automatic"},"customer_balance":null,"konbini":null,"sepa_debit":null,"us_bank_account":null},"payment_method_types":null,"save_default_payment_method":"off"},"pending_invoice_item_interval":null,"pending_setup_intent":null,"pending_update":null,"plan":{"id":"price_1Rauk2R4yt1xFDzX8QKd1XMN","object":"plan","active":true,"amount":500,"amount_decimal":"500","billing_scheme":"per_unit","created":**********,"currency":"usd","interval":"month","interval_count":1,"livemode":false,"metadata":[],"meter":null,"nickname":null,"product":"prod_SVwdp0avHgZEYs","tiers_mode":null,"transform_usage":null,"trial_period_days":null,"usage_type":"licensed"},"quantity":1,"schedule":null,"start_date":**********,"status":"active","test_clock":null,"transfer_data":null,"trial_end":null,"trial_settings":{"end_behavior":{"missing_payment_method":"create_invoice"}},"trial_start":null},"previous_attributes":{"default_payment_method":null,"status":"incomplete"}},"livemode":false,"pending_webhooks":1,"request":{"id":null,"idempotency_key":"33c1781d-39f2-4ce4-a500-3dd11b636d4d"},"type":"customer.subscription.updated"} 
[2025-06-23 12:23:02] local.INFO: Updated user 24 subscription to pro (status: active)  
[2025-06-23 12:23:02] local.INFO: Checkout session completed webhook received {"id":"evt_1Rd9PAR4yt1xFDzX3G5G30m9","object":"event","api_version":"2025-04-30.basil","created":**********,"data":{"object":{"id":"cs_test_a1hk5FRR1e8yEzKA0Sgapos7vJmdmiy2vQxqtKcW6YUJTSA0U9nZZmoT8p","object":"checkout.session","adaptive_pricing":null,"after_expiration":null,"allow_promotion_codes":null,"amount_subtotal":500,"amount_total":500,"automatic_tax":{"enabled":false,"liability":null,"provider":null,"status":null},"billing_address_collection":null,"cancel_url":"http://localhost:8000/plan","client_reference_id":null,"client_secret":null,"collected_information":{"shipping_details":null},"consent":null,"consent_collection":null,"created":**********,"currency":"usd","currency_conversion":null,"custom_fields":[],"custom_text":{"after_submit":null,"shipping_address":null,"submit":null,"terms_of_service_acceptance":null},"customer":"cus_SX3LyAoliTpGmn","customer_creation":null,"customer_details":{"address":{"city":null,"country":"UA","line1":null,"line2":null,"postal_code":null,"state":null},"email":"<EMAIL>","name":"Serhii","phone":null,"tax_exempt":"none","tax_ids":[]},"customer_email":null,"discounts":[],"expires_at":**********,"invoice":"in_1Rd9P7R4yt1xFDzXBIulPnjG","invoice_creation":null,"livemode":false,"locale":null,"metadata":{"plan_name":"pro"},"mode":"subscription","payment_intent":null,"payment_link":null,"payment_method_collection":"always","payment_method_configuration_details":{"id":"pmc_1RJjbmR4yt1xFDzXlwEFeD8e","parent":null},"payment_method_options":{"card":{"request_three_d_secure":"automatic"}},"payment_method_types":["card","link","cashapp","amazon_pay"],"payment_status":"paid","permissions":null,"phone_number_collection":{"enabled":false},"recovered_from":null,"saved_payment_method_options":{"allow_redisplay_filters":["always"],"payment_method_remove":"disabled","payment_method_save":null},"setup_intent":null,"shipping_address_collection":null,"shipping_cost":null,"shipping_options":[],"status":"complete","submit_type":null,"subscription":"sub_1Rd9P7R4yt1xFDzXQ2Oieop9","success_url":"http://localhost:8000/subscription/success/pro","total_details":{"amount_discount":0,"amount_shipping":0,"amount_tax":0},"ui_mode":"hosted","url":null,"wallet_options":null}},"livemode":false,"pending_webhooks":1,"request":{"id":null,"idempotency_key":null},"type":"checkout.session.completed"} 
[2025-06-23 12:23:04] local.INFO: Checkout completed - Updated user 24 subscription to pro  
[2025-06-23 12:23:05] local.INFO: Invoice payment succeeded webhook received {"id":"evt_1Rd9PAR4yt1xFDzX3aXhoPML","object":"event","api_version":"2025-04-30.basil","created":**********,"data":{"object":{"id":"in_1Rd9P7R4yt1xFDzXBIulPnjG","object":"invoice","account_country":"US","account_name":"Kimana LLC sandbox","account_tax_ids":null,"amount_due":500,"amount_overpaid":0,"amount_paid":500,"amount_remaining":0,"amount_shipping":0,"application":null,"attempt_count":1,"attempted":true,"auto_advance":false,"automatic_tax":{"disabled_reason":null,"enabled":false,"liability":null,"provider":null,"status":null},"automatically_finalizes_at":null,"billing_reason":"subscription_create","collection_method":"charge_automatically","created":**********,"currency":"usd","custom_fields":null,"customer":"cus_SX3LyAoliTpGmn","customer_address":null,"customer_email":"<EMAIL>","customer_name":"Serhii","customer_phone":null,"customer_shipping":null,"customer_tax_exempt":"none","customer_tax_ids":[],"default_payment_method":null,"default_source":null,"default_tax_rates":[],"description":null,"discounts":[],"due_date":null,"effective_at":**********,"ending_balance":0,"footer":null,"from_invoice":null,"hosted_invoice_url":"https://invoice.stripe.com/i/acct_1RJjbGR4yt1xFDzX/test_YWNjdF8xUkpqYkdSNHl0MXhGRHpYLF9TWUZ1Sjd5QkhzdDlqc0Q4MlNiTERLN3YzM0h5RkdNLDE0MTIyMjIwMA02004ehCRl6F?s=ap","invoice_pdf":"https://pay.stripe.com/invoice/acct_1RJjbGR4yt1xFDzX/test_YWNjdF8xUkpqYkdSNHl0MXhGRHpYLF9TWUZ1Sjd5QkhzdDlqc0Q4MlNiTERLN3YzM0h5RkdNLDE0MTIyMjIwMA02004ehCRl6F/pdf?s=ap","issuer":{"type":"self"},"last_finalization_error":null,"latest_revision":null,"lines":{"object":"list","data":[{"id":"il_1Rd9P7R4yt1xFDzX7QO1sRnN","object":"line_item","amount":500,"currency":"usd","description":"1 × Pro (at $5.00 / month)","discount_amounts":[],"discountable":true,"discounts":[],"invoice":"in_1Rd9P7R4yt1xFDzXBIulPnjG","livemode":false,"metadata":{"is_on_session_checkout":"true","type":"default","name":"default"},"parent":{"invoice_item_details":null,"subscription_item_details":{"invoice_item":null,"proration":false,"proration_details":{"credited_items":"Over 9 levels deep, aborting normalization"},"subscription":"sub_1Rd9P7R4yt1xFDzXQ2Oieop9","subscription_item":"si_SYFu9yJfP8y0wV"},"type":"subscription_item_details"},"period":{"end":**********,"start":**********},"pretax_credit_amounts":[],"pricing":{"price_details":{"price":"price_1Rauk2R4yt1xFDzX8QKd1XMN","product":"prod_SVwdp0avHgZEYs"},"type":"price_details","unit_amount_decimal":"500"},"quantity":1,"taxes":[]}],"has_more":false,"total_count":1,"url":"/v1/invoices/in_1Rd9P7R4yt1xFDzXBIulPnjG/lines"},"livemode":false,"metadata":[],"next_payment_attempt":null,"number":"BGFN7R5T-0007","on_behalf_of":null,"parent":{"quote_details":null,"subscription_details":{"metadata":{"is_on_session_checkout":"true","type":"default","name":"default"},"subscription":"sub_1Rd9P7R4yt1xFDzXQ2Oieop9"},"type":"subscription_details"},"payment_settings":{"default_mandate":null,"payment_method_options":{"acss_debit":null,"bancontact":null,"card":{"request_three_d_secure":"automatic"},"customer_balance":null,"konbini":null,"sepa_debit":null,"us_bank_account":null},"payment_method_types":null},"period_end":**********,"period_start":**********,"post_payment_credit_notes_amount":0,"pre_payment_credit_notes_amount":0,"receipt_number":null,"rendering":null,"shipping_cost":null,"shipping_details":null,"starting_balance":0,"statement_descriptor":null,"status":"paid","status_transitions":{"finalized_at":**********,"marked_uncollectible_at":null,"paid_at":**********,"voided_at":null},"subtotal":500,"subtotal_excluding_tax":500,"test_clock":null,"total":500,"total_discount_amounts":[],"total_excluding_tax":500,"total_pretax_credit_amounts":[],"total_taxes":[],"webhooks_delivered_at":null}},"livemode":false,"pending_webhooks":1,"request":{"id":null,"idempotency_key":"33c1781d-39f2-4ce4-a500-3dd11b636d4d"},"type":"invoice.payment_succeeded"} 
[2025-06-23 12:28:53] local.INFO: Subscription created webhook received {"id":"evt_1Rd9UpR4yt1xFDzXw7sFxk4a","object":"event","api_version":"2025-04-30.basil","created":**********,"data":{"object":{"id":"sub_1Rd9UnR4yt1xFDzXY7bw7yWO","object":"subscription","application":null,"application_fee_percent":null,"automatic_tax":{"disabled_reason":null,"enabled":false,"liability":null},"billing_cycle_anchor":**********,"billing_cycle_anchor_config":null,"billing_mode":{"type":"classic"},"billing_thresholds":null,"cancel_at":null,"cancel_at_period_end":false,"canceled_at":null,"cancellation_details":{"comment":null,"feedback":null,"reason":null},"collection_method":"charge_automatically","created":**********,"currency":"usd","customer":"cus_SX3LyAoliTpGmn","days_until_due":null,"default_payment_method":null,"default_source":null,"default_tax_rates":[],"description":null,"discounts":[],"ended_at":null,"invoice_settings":{"account_tax_ids":null,"issuer":{"type":"self"}},"items":{"object":"list","data":[{"id":"si_SYG0sbJJn0gI0R","object":"subscription_item","billing_thresholds":null,"created":**********,"current_period_end":**********,"current_period_start":**********,"discounts":[],"metadata":[],"plan":{"id":"price_1Rauk2R4yt1xFDzX8QKd1XMN","object":"plan","active":true,"amount":500,"amount_decimal":"500","billing_scheme":"per_unit","created":**********,"currency":"usd","interval":"month","interval_count":1,"livemode":false,"metadata":[],"meter":null,"nickname":null,"product":"prod_SVwdp0avHgZEYs","tiers_mode":null,"transform_usage":null,"trial_period_days":null,"usage_type":"licensed"},"price":{"id":"price_1Rauk2R4yt1xFDzX8QKd1XMN","object":"price","active":true,"billing_scheme":"per_unit","created":**********,"currency":"usd","custom_unit_amount":null,"livemode":false,"lookup_key":null,"metadata":[],"nickname":null,"product":"prod_SVwdp0avHgZEYs","recurring":{"interval":"month","interval_count":1,"meter":null,"trial_period_days":null,"usage_type":"licensed"},"tax_behavior":"exclusive","tiers_mode":null,"transform_quantity":null,"type":"recurring","unit_amount":500,"unit_amount_decimal":"500"},"quantity":1,"subscription":"sub_1Rd9UnR4yt1xFDzXY7bw7yWO","tax_rates":[]}],"has_more":false,"total_count":1,"url":"/v1/subscription_items?subscription=sub_1Rd9UnR4yt1xFDzXY7bw7yWO"},"latest_invoice":"in_1Rd9UnR4yt1xFDzXIqR5skyW","livemode":false,"metadata":{"is_on_session_checkout":"true","type":"default","name":"default"},"next_pending_invoice_item_invoice":null,"on_behalf_of":null,"pause_collection":null,"payment_settings":{"payment_method_options":{"acss_debit":null,"bancontact":null,"card":{"network":null,"request_three_d_secure":"automatic"},"customer_balance":null,"konbini":null,"sepa_debit":null,"us_bank_account":null},"payment_method_types":null,"save_default_payment_method":"off"},"pending_invoice_item_interval":null,"pending_setup_intent":null,"pending_update":null,"plan":{"id":"price_1Rauk2R4yt1xFDzX8QKd1XMN","object":"plan","active":true,"amount":500,"amount_decimal":"500","billing_scheme":"per_unit","created":**********,"currency":"usd","interval":"month","interval_count":1,"livemode":false,"metadata":[],"meter":null,"nickname":null,"product":"prod_SVwdp0avHgZEYs","tiers_mode":null,"transform_usage":null,"trial_period_days":null,"usage_type":"licensed"},"quantity":1,"schedule":null,"start_date":**********,"status":"incomplete","test_clock":null,"transfer_data":null,"trial_end":null,"trial_settings":{"end_behavior":{"missing_payment_method":"create_invoice"}},"trial_start":null}},"livemode":false,"pending_webhooks":1,"request":{"id":null,"idempotency_key":"a81e0bb9-a1f7-42b3-8ffb-0d0cc8619133"},"type":"customer.subscription.created"} 
[2025-06-23 12:28:53] local.INFO: Updated user 24 subscription to pro  
[2025-06-23 12:28:53] local.INFO: Subscription updated webhook received {"id":"evt_1Rd9UpR4yt1xFDzXVjgRwktU","object":"event","api_version":"2025-04-30.basil","created":**********,"data":{"object":{"id":"sub_1Rd9UnR4yt1xFDzXY7bw7yWO","object":"subscription","application":null,"application_fee_percent":null,"automatic_tax":{"disabled_reason":null,"enabled":false,"liability":null},"billing_cycle_anchor":**********,"billing_cycle_anchor_config":null,"billing_mode":{"type":"classic"},"billing_thresholds":null,"cancel_at":null,"cancel_at_period_end":false,"canceled_at":null,"cancellation_details":{"comment":null,"feedback":null,"reason":null},"collection_method":"charge_automatically","created":**********,"currency":"usd","customer":"cus_SX3LyAoliTpGmn","days_until_due":null,"default_payment_method":"pm_1Rd9UlR4yt1xFDzXTGaPY2N5","default_source":null,"default_tax_rates":[],"description":null,"discounts":[],"ended_at":null,"invoice_settings":{"account_tax_ids":null,"issuer":{"type":"self"}},"items":{"object":"list","data":[{"id":"si_SYG0sbJJn0gI0R","object":"subscription_item","billing_thresholds":null,"created":**********,"current_period_end":**********,"current_period_start":**********,"discounts":[],"metadata":[],"plan":{"id":"price_1Rauk2R4yt1xFDzX8QKd1XMN","object":"plan","active":true,"amount":500,"amount_decimal":"500","billing_scheme":"per_unit","created":**********,"currency":"usd","interval":"month","interval_count":1,"livemode":false,"metadata":[],"meter":null,"nickname":null,"product":"prod_SVwdp0avHgZEYs","tiers_mode":null,"transform_usage":null,"trial_period_days":null,"usage_type":"licensed"},"price":{"id":"price_1Rauk2R4yt1xFDzX8QKd1XMN","object":"price","active":true,"billing_scheme":"per_unit","created":**********,"currency":"usd","custom_unit_amount":null,"livemode":false,"lookup_key":null,"metadata":[],"nickname":null,"product":"prod_SVwdp0avHgZEYs","recurring":{"interval":"month","interval_count":1,"meter":null,"trial_period_days":null,"usage_type":"licensed"},"tax_behavior":"exclusive","tiers_mode":null,"transform_quantity":null,"type":"recurring","unit_amount":500,"unit_amount_decimal":"500"},"quantity":1,"subscription":"sub_1Rd9UnR4yt1xFDzXY7bw7yWO","tax_rates":[]}],"has_more":false,"total_count":1,"url":"/v1/subscription_items?subscription=sub_1Rd9UnR4yt1xFDzXY7bw7yWO"},"latest_invoice":"in_1Rd9UnR4yt1xFDzXIqR5skyW","livemode":false,"metadata":{"is_on_session_checkout":"true","type":"default","name":"default"},"next_pending_invoice_item_invoice":null,"on_behalf_of":null,"pause_collection":null,"payment_settings":{"payment_method_options":{"acss_debit":null,"bancontact":null,"card":{"network":null,"request_three_d_secure":"automatic"},"customer_balance":null,"konbini":null,"sepa_debit":null,"us_bank_account":null},"payment_method_types":null,"save_default_payment_method":"off"},"pending_invoice_item_interval":null,"pending_setup_intent":null,"pending_update":null,"plan":{"id":"price_1Rauk2R4yt1xFDzX8QKd1XMN","object":"plan","active":true,"amount":500,"amount_decimal":"500","billing_scheme":"per_unit","created":**********,"currency":"usd","interval":"month","interval_count":1,"livemode":false,"metadata":[],"meter":null,"nickname":null,"product":"prod_SVwdp0avHgZEYs","tiers_mode":null,"transform_usage":null,"trial_period_days":null,"usage_type":"licensed"},"quantity":1,"schedule":null,"start_date":**********,"status":"active","test_clock":null,"transfer_data":null,"trial_end":null,"trial_settings":{"end_behavior":{"missing_payment_method":"create_invoice"}},"trial_start":null},"previous_attributes":{"default_payment_method":null,"status":"incomplete"}},"livemode":false,"pending_webhooks":1,"request":{"id":null,"idempotency_key":"a81e0bb9-a1f7-42b3-8ffb-0d0cc8619133"},"type":"customer.subscription.updated"} 
[2025-06-23 12:28:53] local.INFO: Updated user 24 subscription to pro (status: active)  
[2025-06-23 12:28:54] local.INFO: Checkout session completed webhook received {"id":"evt_1Rd9UpR4yt1xFDzX2OyfoaX2","object":"event","api_version":"2025-04-30.basil","created":**********,"data":{"object":{"id":"cs_test_a16tg55w4NiyKp55TnVc31tZqvjxIjdVAyEjMlMNzzeow16g10E2pnJCiG","object":"checkout.session","adaptive_pricing":null,"after_expiration":null,"allow_promotion_codes":null,"amount_subtotal":500,"amount_total":500,"automatic_tax":{"enabled":false,"liability":null,"provider":null,"status":null},"billing_address_collection":null,"cancel_url":"http://localhost:8000/plan","client_reference_id":null,"client_secret":null,"collected_information":{"shipping_details":null},"consent":null,"consent_collection":null,"created":**********,"currency":"usd","currency_conversion":null,"custom_fields":[],"custom_text":{"after_submit":null,"shipping_address":null,"submit":null,"terms_of_service_acceptance":null},"customer":"cus_SX3LyAoliTpGmn","customer_creation":null,"customer_details":{"address":{"city":null,"country":"UA","line1":null,"line2":null,"postal_code":null,"state":null},"email":"<EMAIL>","name":"Serhii","phone":null,"tax_exempt":"none","tax_ids":[]},"customer_email":null,"discounts":[],"expires_at":**********,"invoice":"in_1Rd9UnR4yt1xFDzXIqR5skyW","invoice_creation":null,"livemode":false,"locale":null,"metadata":{"plan_name":"pro"},"mode":"subscription","payment_intent":null,"payment_link":null,"payment_method_collection":"always","payment_method_configuration_details":{"id":"pmc_1RJjbmR4yt1xFDzXlwEFeD8e","parent":null},"payment_method_options":{"card":{"request_three_d_secure":"automatic"}},"payment_method_types":["card","link","cashapp","amazon_pay"],"payment_status":"paid","permissions":null,"phone_number_collection":{"enabled":false},"recovered_from":null,"saved_payment_method_options":{"allow_redisplay_filters":["always"],"payment_method_remove":"disabled","payment_method_save":null},"setup_intent":null,"shipping_address_collection":null,"shipping_cost":null,"shipping_options":[],"status":"complete","submit_type":null,"subscription":"sub_1Rd9UnR4yt1xFDzXY7bw7yWO","success_url":"http://localhost:8000/subscription/success/pro","total_details":{"amount_discount":0,"amount_shipping":0,"amount_tax":0},"ui_mode":"hosted","url":null,"wallet_options":null}},"livemode":false,"pending_webhooks":1,"request":{"id":null,"idempotency_key":null},"type":"checkout.session.completed"} 
[2025-06-23 12:28:55] local.INFO: Checkout completed - Updated user 24 subscription to pro  
[2025-06-23 12:28:56] local.INFO: Invoice payment succeeded webhook received {"id":"evt_1Rd9UqR4yt1xFDzXlphFuMvL","object":"event","api_version":"2025-04-30.basil","created":**********,"data":{"object":{"id":"in_1Rd9UnR4yt1xFDzXIqR5skyW","object":"invoice","account_country":"US","account_name":"Kimana LLC sandbox","account_tax_ids":null,"amount_due":500,"amount_overpaid":0,"amount_paid":500,"amount_remaining":0,"amount_shipping":0,"application":null,"attempt_count":1,"attempted":true,"auto_advance":false,"automatic_tax":{"disabled_reason":null,"enabled":false,"liability":null,"provider":null,"status":null},"automatically_finalizes_at":null,"billing_reason":"subscription_create","collection_method":"charge_automatically","created":**********,"currency":"usd","custom_fields":null,"customer":"cus_SX3LyAoliTpGmn","customer_address":null,"customer_email":"<EMAIL>","customer_name":"Serhii","customer_phone":null,"customer_shipping":null,"customer_tax_exempt":"none","customer_tax_ids":[],"default_payment_method":null,"default_source":null,"default_tax_rates":[],"description":null,"discounts":[],"due_date":null,"effective_at":**********,"ending_balance":0,"footer":null,"from_invoice":null,"hosted_invoice_url":"https://invoice.stripe.com/i/acct_1RJjbGR4yt1xFDzX/test_YWNjdF8xUkpqYkdSNHl0MXhGRHpYLF9TWUcwdnZWRDByR0lMbTZ2WndndXpvczZ6enBuZmxvLDE0MTIyMjU1Mg0200Y2G7FKZL?s=ap","invoice_pdf":"https://pay.stripe.com/invoice/acct_1RJjbGR4yt1xFDzX/test_YWNjdF8xUkpqYkdSNHl0MXhGRHpYLF9TWUcwdnZWRDByR0lMbTZ2WndndXpvczZ6enBuZmxvLDE0MTIyMjU1Mg0200Y2G7FKZL/pdf?s=ap","issuer":{"type":"self"},"last_finalization_error":null,"latest_revision":null,"lines":{"object":"list","data":[{"id":"il_1Rd9UnR4yt1xFDzXN4xcZGAd","object":"line_item","amount":500,"currency":"usd","description":"1 × Pro (at $5.00 / month)","discount_amounts":[],"discountable":true,"discounts":[],"invoice":"in_1Rd9UnR4yt1xFDzXIqR5skyW","livemode":false,"metadata":{"is_on_session_checkout":"true","type":"default","name":"default"},"parent":{"invoice_item_details":null,"subscription_item_details":{"invoice_item":null,"proration":false,"proration_details":{"credited_items":"Over 9 levels deep, aborting normalization"},"subscription":"sub_1Rd9UnR4yt1xFDzXY7bw7yWO","subscription_item":"si_SYG0sbJJn0gI0R"},"type":"subscription_item_details"},"period":{"end":**********,"start":**********},"pretax_credit_amounts":[],"pricing":{"price_details":{"price":"price_1Rauk2R4yt1xFDzX8QKd1XMN","product":"prod_SVwdp0avHgZEYs"},"type":"price_details","unit_amount_decimal":"500"},"quantity":1,"taxes":[]}],"has_more":false,"total_count":1,"url":"/v1/invoices/in_1Rd9UnR4yt1xFDzXIqR5skyW/lines"},"livemode":false,"metadata":[],"next_payment_attempt":null,"number":"BGFN7R5T-0008","on_behalf_of":null,"parent":{"quote_details":null,"subscription_details":{"metadata":{"is_on_session_checkout":"true","type":"default","name":"default"},"subscription":"sub_1Rd9UnR4yt1xFDzXY7bw7yWO"},"type":"subscription_details"},"payment_settings":{"default_mandate":null,"payment_method_options":{"acss_debit":null,"bancontact":null,"card":{"request_three_d_secure":"automatic"},"customer_balance":null,"konbini":null,"sepa_debit":null,"us_bank_account":null},"payment_method_types":null},"period_end":**********,"period_start":**********,"post_payment_credit_notes_amount":0,"pre_payment_credit_notes_amount":0,"receipt_number":null,"rendering":null,"shipping_cost":null,"shipping_details":null,"starting_balance":0,"statement_descriptor":null,"status":"paid","status_transitions":{"finalized_at":**********,"marked_uncollectible_at":null,"paid_at":**********,"voided_at":null},"subtotal":500,"subtotal_excluding_tax":500,"test_clock":null,"total":500,"total_discount_amounts":[],"total_excluding_tax":500,"total_pretax_credit_amounts":[],"total_taxes":[],"webhooks_delivered_at":null}},"livemode":false,"pending_webhooks":1,"request":{"id":null,"idempotency_key":"a81e0bb9-a1f7-42b3-8ffb-0d0cc8619133"},"type":"invoice.payment_succeeded"} 
[2025-06-23 12:32:25] local.ERROR: foreach() argument must be of type array|object, null given {"userId":24,"exception":"[object] (ErrorException(code: 0): foreach() argument must be of type array|object, null given at D:\\UpworkProject\\kwog\\app\\Http\\Controllers\\TrainAiModelController.php:61)
[stacktrace]
#0 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(255): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'foreach() argum...', 'D:\\\\UpworkProjec...', 61)
#1 D:\\UpworkProject\\kwog\\app\\Http\\Controllers\\TrainAiModelController.php(61): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'foreach() argum...', 'D:\\\\UpworkProjec...', 61)
#2 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\TrainAiModelController->submit_training_documents(Object(Illuminate\\Http\\Request))
#3 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('submit_training...', Array)
#4 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\TrainAiModelController), 'submit_training...')
#5 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#6 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#7 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#8 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php(41): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#10 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#11 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#12 D:\\UpworkProject\\kwog\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Inertia\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 D:\\UpworkProject\\kwog\\vendor\\laravel\\jetstream\\src\\Http\\Middleware\\ShareInertiaData.php(69): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Laravel\\Jetstream\\Http\\Middleware\\ShareInertiaData->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\AuthenticateSession.php(60): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\AuthenticateSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'sanctum')
#22 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#28 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#35 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#36 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#37 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#38 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#39 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#40 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#56 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#57 D:\\UpworkProject\\kwog\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#58 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('D:\\\\UpworkProjec...')
#59 {main}
"} 
[2025-06-23 12:32:48] local.ERROR: Malformed UTF-8 characters, possibly incorrectly encoded {"userId":24,"exception":"[object] (InvalidArgumentException(code: 0): Malformed UTF-8 characters, possibly incorrectly encoded at D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\JsonResponse.php:88)
[stacktrace]
#0 D:\\UpworkProject\\kwog\\vendor\\symfony\\http-foundation\\JsonResponse.php(49): Illuminate\\Http\\JsonResponse->setData(Array)
#1 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\JsonResponse.php(32): Symfony\\Component\\HttpFoundation\\JsonResponse->__construct(Array, 200, Array, false)
#2 D:\\UpworkProject\\kwog\\vendor\\inertiajs\\inertia-laravel\\src\\Response.php(107): Illuminate\\Http\\JsonResponse->__construct(Array, 200, Array)
#3 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(900): Inertia\\Response->toResponse(Object(Illuminate\\Http\\Request))
#4 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(885): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Inertia\\Response))
#5 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Inertia\\Response))
#6 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#7 D:\\UpworkProject\\kwog\\app\\Http\\Middleware\\Onboarding.php(35): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#8 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\Onboarding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#9 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php(41): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#10 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#11 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#12 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#13 D:\\UpworkProject\\kwog\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#14 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Inertia\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#15 D:\\UpworkProject\\kwog\\vendor\\laravel\\jetstream\\src\\Http\\Middleware\\ShareInertiaData.php(69): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Laravel\\Jetstream\\Http\\Middleware\\ShareInertiaData->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\AuthenticateSession.php(60): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\AuthenticateSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'sanctum')
#23 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#29 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#36 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#37 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#38 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#39 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#40 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#41 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#57 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#58 D:\\UpworkProject\\kwog\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#59 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('D:\\\\UpworkProjec...')
#60 {main}
"} 
[2025-06-23 12:33:03] local.ERROR: Malformed UTF-8 characters, possibly incorrectly encoded {"userId":24,"exception":"[object] (InvalidArgumentException(code: 0): Malformed UTF-8 characters, possibly incorrectly encoded at D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\JsonResponse.php:88)
[stacktrace]
#0 D:\\UpworkProject\\kwog\\vendor\\symfony\\http-foundation\\JsonResponse.php(49): Illuminate\\Http\\JsonResponse->setData(Array)
#1 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\JsonResponse.php(32): Symfony\\Component\\HttpFoundation\\JsonResponse->__construct(Array, 200, Array, false)
#2 D:\\UpworkProject\\kwog\\vendor\\inertiajs\\inertia-laravel\\src\\Response.php(107): Illuminate\\Http\\JsonResponse->__construct(Array, 200, Array)
#3 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(900): Inertia\\Response->toResponse(Object(Illuminate\\Http\\Request))
#4 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(885): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Inertia\\Response))
#5 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Inertia\\Response))
#6 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#7 D:\\UpworkProject\\kwog\\app\\Http\\Middleware\\Onboarding.php(35): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#8 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\Onboarding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#9 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php(41): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#10 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#11 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#12 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#13 D:\\UpworkProject\\kwog\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#14 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Inertia\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#15 D:\\UpworkProject\\kwog\\vendor\\laravel\\jetstream\\src\\Http\\Middleware\\ShareInertiaData.php(69): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Laravel\\Jetstream\\Http\\Middleware\\ShareInertiaData->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\AuthenticateSession.php(60): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\AuthenticateSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'sanctum')
#23 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#29 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#36 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#37 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#38 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#39 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#40 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#41 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#57 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#58 D:\\UpworkProject\\kwog\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#59 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('D:\\\\UpworkProjec...')
#60 {main}
"} 
[2025-06-23 12:33:09] local.ERROR: Malformed UTF-8 characters, possibly incorrectly encoded {"userId":24,"exception":"[object] (InvalidArgumentException(code: 0): Malformed UTF-8 characters, possibly incorrectly encoded at D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\JsonResponse.php:88)
[stacktrace]
#0 D:\\UpworkProject\\kwog\\vendor\\symfony\\http-foundation\\JsonResponse.php(49): Illuminate\\Http\\JsonResponse->setData(Array)
#1 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\JsonResponse.php(32): Symfony\\Component\\HttpFoundation\\JsonResponse->__construct(Array, 200, Array, false)
#2 D:\\UpworkProject\\kwog\\vendor\\inertiajs\\inertia-laravel\\src\\Response.php(107): Illuminate\\Http\\JsonResponse->__construct(Array, 200, Array)
#3 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(900): Inertia\\Response->toResponse(Object(Illuminate\\Http\\Request))
#4 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(885): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Inertia\\Response))
#5 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Inertia\\Response))
#6 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#7 D:\\UpworkProject\\kwog\\app\\Http\\Middleware\\Onboarding.php(35): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#8 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\Onboarding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#9 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php(41): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#10 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#11 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#12 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#13 D:\\UpworkProject\\kwog\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#14 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Inertia\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#15 D:\\UpworkProject\\kwog\\vendor\\laravel\\jetstream\\src\\Http\\Middleware\\ShareInertiaData.php(69): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Laravel\\Jetstream\\Http\\Middleware\\ShareInertiaData->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\AuthenticateSession.php(60): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\AuthenticateSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'sanctum')
#23 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#29 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#36 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#37 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#38 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#39 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#40 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#41 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#57 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#58 D:\\UpworkProject\\kwog\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#59 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('D:\\\\UpworkProjec...')
#60 {main}
"} 
[2025-06-23 12:35:19] local.ERROR: Malformed UTF-8 characters, possibly incorrectly encoded {"userId":24,"exception":"[object] (InvalidArgumentException(code: 0): Malformed UTF-8 characters, possibly incorrectly encoded at D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\JsonResponse.php:88)
[stacktrace]
#0 D:\\UpworkProject\\kwog\\vendor\\symfony\\http-foundation\\JsonResponse.php(49): Illuminate\\Http\\JsonResponse->setData(Array)
#1 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\JsonResponse.php(32): Symfony\\Component\\HttpFoundation\\JsonResponse->__construct(Array, 200, Array, false)
#2 D:\\UpworkProject\\kwog\\vendor\\inertiajs\\inertia-laravel\\src\\Response.php(107): Illuminate\\Http\\JsonResponse->__construct(Array, 200, Array)
#3 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(900): Inertia\\Response->toResponse(Object(Illuminate\\Http\\Request))
#4 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(885): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Inertia\\Response))
#5 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Inertia\\Response))
#6 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#7 D:\\UpworkProject\\kwog\\app\\Http\\Middleware\\Onboarding.php(35): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#8 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\Onboarding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#9 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php(41): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#10 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#11 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#12 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#13 D:\\UpworkProject\\kwog\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#14 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Inertia\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#15 D:\\UpworkProject\\kwog\\vendor\\laravel\\jetstream\\src\\Http\\Middleware\\ShareInertiaData.php(69): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Laravel\\Jetstream\\Http\\Middleware\\ShareInertiaData->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\AuthenticateSession.php(60): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\AuthenticateSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'sanctum')
#23 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#29 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#36 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#37 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#38 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#39 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#40 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#41 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#57 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#58 D:\\UpworkProject\\kwog\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#59 D:\\UpworkProject\\kwog\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('D:\\\\UpworkProjec...')
#60 {main}
"} 
