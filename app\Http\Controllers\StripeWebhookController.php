<?php

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use <PERSON><PERSON>\Cashier\Http\Controllers\WebhookController as CashierController;

class StripeWebhookController extends CashierController
{

    public function handleWebhook(Request $request)
    {
        // Get the webhook secret from environment variables
        $webhookSecret = env('STRIPE_WEBHOOK_SECRET');
        
        if (empty($webhookSecret)) {
            Log::error('Stripe webhook secret not configured');
            return response('Webhook secret not configured', 500);
        }
        
        // The parent class (Laravel Cashier's WebhookController) will use this secret
        // to verify the signature of incoming webhook requests
        return parent::handleWebhook($request);
    }
    /**
     * Handle a successful customer subscription created.
     */
    public function handleCustomerSubscriptionCreated(array $payload)
    {
        Log::info('Subscription created webhook received', $payload);
        
        $data = $payload['data']['object'];
        $user = $this->getUserByStripeId($data['customer']);
        
        if ($user) {
            $planName = $this->getPlanNameFromPriceId($data['items']['data'][0]['price']['id']);
            $user->subscription_plan = $planName;
            $user->save();
            
            Log::info("Updated user {$user->id} subscription to {$planName}");
        }
    }

    /**
     * Handle a successful customer subscription updated.
     */
    public function handleCustomerSubscriptionUpdated(array $payload)
    {
        Log::info('Subscription updated webhook received', $payload);
        
        $data = $payload['data']['object'];
        $user = $this->getUserByStripeId($data['customer']);
        
        if ($user) {
            $status = $data['status'];
            $planName = $this->getPlanNameFromPriceId($data['items']['data'][0]['price']['id']);
            
            // Update subscription status based on Stripe status
            switch ($status) {
                case 'active':
                    $user->subscription_plan = $planName;
                    break;
                case 'canceled':
                case 'incomplete_expired':
                    $user->subscription_plan = 'free';
                    break;
                case 'past_due':
                case 'unpaid':
                    // Keep current plan but you might want to limit features
                    break;
            }
            
            $user->save();
            Log::info("Updated user {$user->id} subscription to {$user->subscription_plan} (status: {$status})");
        }
    }

    /**
     * Handle a customer subscription deleted.
     */
    public function handleCustomerSubscriptionDeleted(array $payload)
    {
        Log::info('Subscription deleted webhook received', $payload);
        
        $data = $payload['data']['object'];
        $user = $this->getUserByStripeId($data['customer']);
        
        if ($user) {
            $user->subscription_plan = 'free';
            $user->save();
            
            Log::info("Reset user {$user->id} subscription to free");
        }
    }

    /**
     * Handle a successful invoice payment.
     */
    public function handleInvoicePaymentSucceeded(array $payload)
    {
        Log::info('Invoice payment succeeded webhook received', $payload);
        
        $data = $payload['data']['object'];
        $user = $this->getUserByStripeId($data['customer']);
        
        if ($user && isset($data['subscription'])) {
            // Ensure user has active subscription
            $planName = $this->getPlanNameFromPriceId($data['lines']['data'][0]['price']['id']);
            $user->subscription_plan = $planName;
            $user->save();
            
            Log::info("Confirmed user {$user->id} subscription: {$planName}");
        }
    }

    /**
     * Handle a failed invoice payment.
     */
    public function handleInvoicePaymentFailed(array $payload)
    {
        Log::info('Invoice payment failed webhook received', $payload);
        
        $data = $payload['data']['object'];
        $user = $this->getUserByStripeId($data['customer']);
        
        if ($user) {
            // You might want to send notification email or take other actions
            Log::warning("Payment failed for user {$user->id}");
        }
    }

    /**
     * Handle checkout session completed.
     */
    public function handleCheckoutSessionCompleted(array $payload)
    {
        Log::info('Checkout session completed webhook received', $payload);
        
        $data = $payload['data']['object'];
        
        if ($data['mode'] === 'subscription') {
            $user = $this->getUserByStripeId($data['customer']);
            
            if ($user) {
                // Get subscription details
                $subscriptionId = $data['subscription'];
                $stripe = new \Stripe\StripeClient(config('cashier.secret'));
                $subscription = $stripe->subscriptions->retrieve($subscriptionId);
                
                $planName = $this->getPlanNameFromPriceId($subscription->items->data[0]->price->id);
                $user->subscription_plan = $planName;
                $user->save();
                
                Log::info("Checkout completed - Updated user {$user->id} subscription to {$planName}");
            }
        }
    }

    /**
     * Get user by Stripe customer ID.
     */
    protected function getUserByStripeId($stripeId)
    {
        return User::where('stripe_id', $stripeId)->first();
    }

    /**
     * Map Stripe price ID to plan name.
     */
    protected function getPlanNameFromPriceId($priceId)
    {
        $priceMap = [
            env('STRIPE_PRO_PRICE_ID') => 'pro',
            env('STRIPE_ENTERPRISE_PRICE_ID') => 'enterprise',
        ];

        return $priceMap[$priceId] ?? 'free';
    }
} 