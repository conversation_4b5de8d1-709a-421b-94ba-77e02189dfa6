# Qdrant Migration Guide

This document outlines the migration from Pinecone to Qdrant vector database for the chatbot application.

## Overview

The application has been migrated from using Pinecone's vector database service to Qdrant, an open-source vector database. This change provides:

- **Cost Savings**: Qdrant can be self-hosted, eliminating subscription costs
- **Better Control**: Full control over the vector database infrastructure
- **Performance**: Optimized for high-performance vector similarity search
- **Flexibility**: More configuration options and customization possibilities

## Changes Made

### 1. Configuration
- Added `config/qdrant.php` configuration file
- Updated `.env.example` with Qdrant configuration variables
- Removed Pinecone API key dependencies from controllers

### 2. Services
- Created `QdrantService` class for vector operations
- Updated `VectorService` to use Qdrant instead of local database calculations
- Implemented vector storage, search, and deletion methods

### 3. Controllers Updated
- `ChatController`: Now uses VectorService with Qdrant for similarity search
- `TrainingMaterialsController`: Processes files and stores vectors in Qdrant
- `TrainAiModelController`: Removed Pinecone assistant creation, focuses on vector processing
- `FaqResponsesController`: Removed Pinecone dependencies
- `CustomizeChatbotController`: Removed Pinecone dependencies

### 4. Migration Tools
- Created `MigrateToQdrant` command for migrating existing vector data
- Added comprehensive tests for Qdrant integration

## Setup Instructions

### 1. Install Qdrant

#### Option A: Docker (Recommended)
```bash
docker run -p 6333:6333 qdrant/qdrant
```

#### Option B: Local Installation
Follow the [official Qdrant installation guide](https://qdrant.tech/documentation/quick-start/)

### 2. Configure Environment Variables

Add the following to your `.env` file:

```env
QDRANT_HOST=localhost
QDRANT_PORT=6333
QDRANT_API_KEY=
QDRANT_TIMEOUT=30
QDRANT_COLLECTION_NAME=chatbot_vectors
QDRANT_VECTOR_SIZE=1536
QDRANT_DISTANCE_METRIC=Cosine
QDRANT_VERIFY_SSL=true
```

### 3. Install Dependencies

The Qdrant PHP client is already included in `composer.json`:

```bash
composer install
```

### 4. Migrate Existing Data (if applicable)

If you have existing vector data in your local database, run the migration command:

```bash
php artisan migrate:qdrant
```

Options:
- `--batch-size=100`: Number of vectors to process in each batch (default: 100)

### 5. Test the Integration

Run the Qdrant integration tests:

```bash
php artisan test tests/Feature/QdrantIntegrationTest.php
```

## Usage

### Vector Operations

The application now uses Qdrant for all vector operations:

1. **Storing Vectors**: When training materials are uploaded, they are processed into chunks, embedded using OpenAI, and stored in Qdrant
2. **Searching Vectors**: Chat queries are embedded and searched against Qdrant for relevant context
3. **Deleting Vectors**: When training materials are deleted, associated vectors are removed from Qdrant

### Chat Flow

1. User sends a message
2. Message is embedded using OpenAI's text-embedding-ada-002 model
3. Qdrant searches for similar vectors based on chatbot ID
4. Relevant text chunks are retrieved and used as context
5. OpenAI generates a response using the context

## Monitoring and Maintenance

### Health Checks
- Qdrant provides a REST API for health checks: `GET http://localhost:6333/`
- Collection info can be retrieved: `GET http://localhost:6333/collections/{collection_name}`

### Performance Tuning
- Adjust `QDRANT_VECTOR_SIZE` if using different embedding models
- Modify distance metric (`Cosine`, `Dot`, `Euclid`) based on your use case
- Configure collection parameters in `config/qdrant.php`

### Backup and Recovery
- Qdrant supports snapshots for backup
- Consider implementing regular backup procedures for production

## Troubleshooting

### Common Issues

1. **Connection Errors**
   - Ensure Qdrant is running on the configured host and port
   - Check firewall settings if using remote Qdrant instance

2. **Collection Not Found**
   - The application automatically creates collections on first use
   - Verify collection configuration in `config/qdrant.php`

3. **Vector Dimension Mismatch**
   - Ensure `QDRANT_VECTOR_SIZE` matches your embedding model's output size
   - OpenAI's text-embedding-ada-002 produces 1536-dimensional vectors

4. **Performance Issues**
   - Consider increasing batch sizes for bulk operations
   - Monitor Qdrant resource usage and scale accordingly

## Migration Rollback

If you need to rollback to the previous system:

1. Restore the original controller files from version control
2. Remove Qdrant configuration from `.env`
3. Ensure local vector calculations are working in `VectorService`

Note: This migration removes Pinecone dependencies entirely, so a rollback would require restoring Pinecone configuration and API keys.
