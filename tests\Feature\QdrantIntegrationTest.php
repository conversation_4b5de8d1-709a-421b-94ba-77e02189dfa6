<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Services\QdrantService;
use App\Services\VectorService;
use Illuminate\Foundation\Testing\RefreshDatabase;

class QdrantIntegrationTest extends TestCase
{
    use RefreshDatabase;

    private QdrantService $qdrantService;
    private VectorService $vectorService;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Skip tests if Qdrant is not configured
        if (!config('qdrant.host')) {
            $this->markTestSkipped('Qdrant is not configured');
        }

        $this->qdrantService = new QdrantService();
        $this->vectorService = new VectorService();
    }

    public function test_qdrant_service_can_be_instantiated()
    {
        $this->assertInstanceOf(QdrantService::class, $this->qdrantService);
    }

    public function test_can_get_collection_info()
    {
        $info = $this->qdrantService->getCollectionInfo();
        $this->assertIsArray($info);
    }

    public function test_can_upsert_and_search_vectors()
    {
        // Create test vectors
        $testVectors = [
            [
                'id' => 1,
                'vector' => array_fill(0, 1536, 0.1), // Mock embedding vector
                'payload' => [
                    'chatbot_id' => 1,
                    'text_id' => 1,
                    'content' => 'Test content 1'
                ]
            ],
            [
                'id' => 2,
                'vector' => array_fill(0, 1536, 0.2), // Mock embedding vector
                'payload' => [
                    'chatbot_id' => 1,
                    'text_id' => 2,
                    'content' => 'Test content 2'
                ]
            ]
        ];

        // Upsert vectors
        $result = $this->qdrantService->upsertVectors($testVectors);
        $this->assertTrue($result);

        // Search for similar vectors
        $queryVector = array_fill(0, 1536, 0.15); // Query vector similar to first test vector
        $searchResults = $this->qdrantService->searchSimilarVectors($queryVector, 2, ['chatbot_id' => 1]);

        $this->assertIsArray($searchResults);
        $this->assertNotEmpty($searchResults);
        
        // Clean up
        $this->qdrantService->deleteVectors([1, 2]);
    }

    public function test_can_delete_vectors()
    {
        // Create test vector
        $testVector = [
            [
                'id' => 999,
                'vector' => array_fill(0, 1536, 0.5),
                'payload' => ['test' => true]
            ]
        ];

        // Upsert vector
        $this->qdrantService->upsertVectors($testVector);

        // Delete vector
        $result = $this->qdrantService->deleteVectors([999]);
        $this->assertTrue($result);
    }

    public function test_can_delete_vectors_by_filter()
    {
        // Create test vectors with specific filter criteria
        $testVectors = [
            [
                'id' => 1001,
                'vector' => array_fill(0, 1536, 0.1),
                'payload' => ['chatbot_id' => 999, 'test_batch' => true]
            ],
            [
                'id' => 1002,
                'vector' => array_fill(0, 1536, 0.2),
                'payload' => ['chatbot_id' => 999, 'test_batch' => true]
            ]
        ];

        // Upsert vectors
        $this->qdrantService->upsertVectors($testVectors);

        // Delete by filter
        $result = $this->qdrantService->deleteVectorsByFilter(['chatbot_id' => 999]);
        $this->assertTrue($result);
    }

    public function test_vector_service_integration()
    {
        // Test VectorService methods that use QdrantService
        $testVector = array_fill(0, 1536, 0.3);
        $metadata = [
            'chatbot_id' => 1,
            'text_id' => 1,
            'content' => 'Vector service test'
        ];

        // Store vector using VectorService
        $result = $this->vectorService->storeVector(1003, $testVector, $metadata);
        $this->assertTrue($result);

        // Search for similar vectors
        $queryVector = array_fill(0, 1536, 0.31);
        $results = $this->vectorService->getMostSimilarVectors($queryVector, 1, 1);
        
        $this->assertIsArray($results);
        
        // Clean up
        $this->vectorService->deleteVector(1003);
    }

    protected function tearDown(): void
    {
        // Clean up any remaining test data
        try {
            $this->qdrantService->deleteVectorsByFilter(['test_batch' => true]);
        } catch (\Exception $e) {
            // Ignore cleanup errors
        }

        parent::tearDown();
    }
}
