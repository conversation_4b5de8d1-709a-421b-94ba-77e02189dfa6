<?php

namespace App\Http\Controllers;

use Inertia\Inertia;
use App\Models\AiFaq;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;

class FaqResponsesController extends Controller
{
    // Removed Pinecone-specific properties as we're now using Qdrant
    // Vector operations are handled through VectorService

    public function __construct()
    {
        // Constructor no longer needs Pinecone configuration
    }

    public function faq_responses() { 
        $faqs = AiFaq::where('user_id', auth()->user()->id)->get();

        return Inertia::render('FaqResponses/FaqResponses', [ 
            'chatbots' => $this->chatbots(),
            'currently_selected_chatbot' => $this->selectedBot(),
            'faqs' => $faqs
        ]);
        
    }

    public function submit_faq(Request $request) { 
        AiFaq::create([
            'chatbot_id' => $request->chatbot_id,
            'user_id' => auth()->user()->id,
            'question' => $request->question,
            'answer' => $request->answer,
        ]);

        $this->updateAssistantInstructions();

        return redirect()->back();
        
    }
    
    public function delete_faqs(Request $request) {  

        $ids = collect($request->selectedFaqs)->pluck('id');
        
        AiFaq::whereIn('id', $ids)->delete();

        $this->updateAssistantInstructions();

        return redirect()->back();
        
    }

    private function updateAssistantInstructions()
    {
        $userId = auth()->user()->id;
        $chatbot = $this->selectedBot();
        $chatbotId = $chatbot->id;

        
        $assistant_name = 'assistant' . $userId . $chatbotId;
        
        $add_faq = '';
        $frequently_asked_questions = AiFaq::where('user_id', $userId)->get();
        
        if ($frequently_asked_questions->count() > 0) {
            $add_faq = "Before answering any question, check if it matches one of these FAQs. If yes, respond with the given answer:\n\n";
            foreach ($frequently_asked_questions as $faq) {
                $add_faq .= "Question: {$faq->question}\nAnswer: {$faq->answer}\n\n";
            }
        }

        $instructions = <<<TEXT
            You are an AI assistant designed to provide clear, concise, and accurate answers strictly based on the uploaded documents. Keep responses brief and to the point, ensuring they directly address the question. If the relevant information is not available in the documents, simply state that the answer is not provided. Do not mention document names, internal processing, or system functionality. Your primary goal is to deliver useful, factual responses in the most straightforward manner possible.

            When someone asks what you help with, do not respond with generic phrases like:
            "I can assist with providing clear, concise, and accurate answers..."

            If you don't have info on a topic, don't say: "The information about X is not provided in the available snippets."
            Instead, say something like: "I don't have information on that, but I’d be happy to help with anything related to [ENTITY_NAME]."

            Always mention the actual entity's name (as seen in the files) instead of saying "company", "entity", or "individual".

            Also, never talk about uploaded documents — the user doesn't know about them and they don't care.

            Be able to answer simple questions like greetings!
            TEXT;

        $instructions .= "\n\n" . $add_faq;

        $response = Http::withHeaders([
            'Api-Key' => $this->apiKey,
            'Content-Type' => 'application/json',
            'X-Pinecone-API-Version' => '2025-01',
        ])->patch("{$this->baseUrl}/assistants/{$assistant_name}", [
            'instructions' => $instructions,
            'region' => 'us',
        ]);

    }

}

 