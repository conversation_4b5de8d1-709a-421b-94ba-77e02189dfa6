<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\TextVector;
use App\Models\TextData;
use App\Services\QdrantService;
use App\Services\VectorService;

class MigrateToQdrant extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'migrate:qdrant {--batch-size=100 : Number of vectors to process in each batch}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Migrate existing vector data from local database to Qdrant';

    private QdrantService $qdrantService;
    private VectorService $vectorService;

    public function __construct()
    {
        parent::__construct();
        $this->qdrantService = new QdrantService();
        $this->vectorService = new VectorService();
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting migration to Qdrant...');

        $batchSize = $this->option('batch-size');
        $totalVectors = TextVector::count();

        if ($totalVectors === 0) {
            $this->info('No vectors found to migrate.');
            return 0;
        }

        $this->info("Found {$totalVectors} vectors to migrate.");

        $bar = $this->output->createProgressBar($totalVectors);
        $bar->start();

        $processed = 0;
        $errors = 0;

        TextVector::chunk($batchSize, function ($vectors) use (&$processed, &$errors, $bar) {
            $vectorsToUpsert = [];

            foreach ($vectors as $vector) {
                try {
                    // Get associated text data
                    $textData = TextData::find($vector->text_id);
                    
                    $metadata = [
                        'chatbot_id' => $vector->chatbot_id,
                        'user_id' => $vector->user_id,
                        'training_material_id' => $vector->training_material_id,
                        'text_id' => $vector->text_id,
                        'text_content' => $textData ? $textData->text : '',
                        'migrated_at' => now()->toDateTimeString(),
                    ];

                    $vectorsToUpsert[] = [
                        'id' => $vector->id,
                        'vector' => json_decode($vector->vector, true),
                        'payload' => $metadata
                    ];

                } catch (\Exception $e) {
                    $this->error("Error processing vector {$vector->id}: " . $e->getMessage());
                    $errors++;
                }

                $bar->advance();
                $processed++;
            }

            // Batch upsert to Qdrant
            if (!empty($vectorsToUpsert)) {
                try {
                    $success = $this->qdrantService->upsertVectors($vectorsToUpsert);
                    if (!$success) {
                        $this->error("Failed to upsert batch to Qdrant");
                        $errors += count($vectorsToUpsert);
                    }
                } catch (\Exception $e) {
                    $this->error("Error upserting batch to Qdrant: " . $e->getMessage());
                    $errors += count($vectorsToUpsert);
                }
            }
        });

        $bar->finish();
        $this->newLine();

        $successful = $processed - $errors;
        $this->info("Migration completed!");
        $this->info("Total processed: {$processed}");
        $this->info("Successful: {$successful}");
        $this->info("Errors: {$errors}");

        if ($errors > 0) {
            $this->warn("Some vectors failed to migrate. Check the logs for details.");
            return 1;
        }

        $this->info("All vectors successfully migrated to Qdrant!");
        
        // Verify migration
        $this->info("Verifying migration...");
        $qdrantCount = $this->qdrantService->countVectors();
        $this->info("Vectors in Qdrant: {$qdrantCount}");
        
        if ($qdrantCount >= $successful) {
            $this->info("Migration verification successful!");
        } else {
            $this->warn("Migration verification failed. Expected at least {$successful} vectors, found {$qdrantCount}");
        }

        return 0;
    }
}
