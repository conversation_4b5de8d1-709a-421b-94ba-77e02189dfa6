<?php

namespace App\Http\Controllers;

use Inertia\Inertia;
use App\Models\TextData;
use App\Models\TextVector;
use Illuminate\Http\Request;
use App\Models\TrainingMaterial;
use Illuminate\Http\UploadedFile;
use OpenAI\Laravel\Facades\OpenAI;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Http;
use Sastrawi\Stemmer\StemmerFactory;
use App\Services\TextProcessingService;
use Illuminate\Support\Facades\Storage;

class TrainingMaterialsController extends Controller
{
    // Removed Pinecone-specific properties as we're now using Qdrant
    // Vector operations are handled through VectorService

    public function __construct()
    {
        // Constructor no longer needs Pinecone configuration
    }
     
    public function training_materials() {
 
        $training_materials = TrainingMaterial::where('user_id', auth()->user()->id)->get();

        $chatbot = $this->selectedBot(); 
        
        if ($chatbot->training_status == false) { 
            $pdf_path = 'trainingfiles/' . auth()->user()->id .'/'. $chatbot->id .'/pdfs';
        
            if (Storage::disk('public')->exists($pdf_path)) {
                $files = Storage::disk('public')->files($pdf_path);

                if (count($files) > 0){

                    $assistant_name = 'assistant' . auth()->user()->id . $chatbot->id ;

                    if (!empty($files)) {
                        foreach ($files as $file) {
                            $filePath = Storage::disk('public')->path($file);
                            $uploadedFile = new UploadedFile(
                                $filePath,
                                basename($file),
                                mime_content_type($filePath),
                                null,
                                true
                            );

                            $uploadedFiles[] = $uploadedFile;
                        }
                    }

                    $this->uploadFilesToAssistant($assistant_name, $uploadedFiles);
                }
            }   

            $txt_path = 'trainingfiles/' . auth()->user()->id .'/'. $chatbot->id .'/linktxtfiles';

            
            if (Storage::disk('public')->exists($txt_path)) {
                $files = Storage::disk('public')->files($txt_path);

                if (count($files) > 0){

                    $assistant_name = 'assistant' . auth()->user()->id . $chatbot->id ;

                    if (!empty($files)) {
                        foreach ($files as $file) {
                            $filePath = Storage::disk('public')->path($file);
                            $uploadedFile = new UploadedFile(
                                $filePath,
                                basename($file),
                                mime_content_type($filePath),
                                null,
                                true
                            );

                            $uploadedFiles[] = $uploadedFile;
                        }
                    }

                    $this->uploadFilesToAssistant($assistant_name, $uploadedFiles);
                }
            } 

        } 
        
        return Inertia::render('TrainingMaterials/TrainingMaterials', [ 
            'chatbots' => $this->chatbots(),
            'currently_selected_chatbot' => $this->selectedBot(),
            'training_materials' => $training_materials,
        ]);
    }

    public function delete_training_material(Request $request) {

        $userId = auth()->user()->id;
        $chatbot = $this->selectedBot();
        $chatbotId = $chatbot->id;

        // Delete vectors from Qdrant for the selected materials
        $vectorService = new VectorService();

        foreach ($request->selectedMaterial as $material) {
            // Delete vectors associated with this training material
            $trainingMaterialId = $material['id'];

            // Delete vectors from Qdrant using training material ID filter
            $vectorService->deleteVectorsByFilter(['training_material_id' => $trainingMaterialId]);

            // Also delete from local TextVector table if needed
            TextVector::where('training_material_id', $trainingMaterialId)->delete();
        }

        foreach ($request->selectedMaterial as $file) {
            if ($file['material_type'] == 'File') {
                $filePath = 'trainingfiles/' . $userId . '/' . $chatbotId . '/pdfs/' .$file['material_title']; 
                
            }else {
                $filePath = 'trainingfiles/' . $userId . '/' . $chatbotId . '/linktxtfiles/' . $file['random_id'] . '.txt';
            }

            // Delete the file from storage
            if (Storage::disk('public')->exists($filePath)) {
                Storage::disk('public')->delete($filePath);
            }
        } 

        $ids = collect($request->selectedMaterial)->pluck('id');
        TrainingMaterial::whereIn('id', $ids)->delete();
        
        return redirect()->back();
    }

    // Process files and store vectors in Qdrant
    private function processFilesForVectorStorage($files, $chatbotId, $trainingMaterialId) {
        $vectorService = new VectorService();

        foreach ($files as $file) {
            // Process file content and create embeddings
            $content = file_get_contents($file->getRealPath());

            // Split content into chunks for better vector search
            $chunks = $this->splitTextIntoChunks($content, 1000); // 1000 character chunks

            foreach ($chunks as $index => $chunk) {
                try {
                    // Create embedding for the chunk
                    $embedding = OpenAI::embeddings()->create([
                        'model' => 'text-embedding-ada-002',
                        'input' => $chunk,
                    ]);

                    // Store text data
                    $textData = TextData::create([
                        'text' => $chunk,
                        'chatbot_id' => $chatbotId,
                        'training_material_id' => $trainingMaterialId
                    ]);

                    // Store vector in Qdrant
                    $vectorId = $textData->id;
                    $metadata = [
                        'chatbot_id' => $chatbotId,
                        'training_material_id' => $trainingMaterialId,
                        'text_id' => $textData->id,
                        'file_name' => $file->getClientOriginalName(),
                        'chunk_index' => $index,
                        'uploaded_at' => now()->toDateTimeString(),
                    ];

                    $vectorService->storeVector(
                        $vectorId,
                        $embedding['data'][0]['embedding'],
                        $metadata
                    );

                    // Also store in local database for backup
                    TextVector::create([
                        'vector' => json_encode($embedding['data'][0]['embedding']),
                        'user_id' => auth()->user()->id,
                        'chatbot_id' => $chatbotId,
                        'training_material_id' => $trainingMaterialId,
                        'text_id' => $textData->id
                    ]);

                } catch (\Exception $e) {
                    \Log::error('Error processing file chunk: ' . $e->getMessage());
                }
            }
        }

        // Update chatbot training status
        $chatbot = $this->selectedBot();
        $chatbot->forceFill([
            'training_status' => true,
        ])->save();
    }

    private function splitTextIntoChunks($text, $chunkSize = 1000) {
        $chunks = [];
        $textLength = strlen($text);

        for ($i = 0; $i < $textLength; $i += $chunkSize) {
            $chunks[] = substr($text, $i, $chunkSize);
        }

        return $chunks;
    }
    
 
}
